// Login automation script for Mission Fresh app testing
import puppeteer from 'puppeteer';

async function loginAndTestSidebar() {
  console.log('🚀 Starting Mission Fresh app login and sidebar testing...');
  
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: ['--start-maximized']
  });
  
  const page = await browser.newPage();
  
  try {
    // Navigate to auth page
    console.log('📍 Navigating to auth page...');
    await page.goto('http://127.0.0.1:5003/auth', { waitUntil: 'networkidle0' });
    
    // Take screenshot of auth page
    await page.screenshot({ path: '/tmp/auth_page_puppeteer.png', fullPage: true });
    console.log('📸 Auth page screenshot saved');
    
    // Fill login form
    console.log('📝 Filling login form...');
    await page.waitForSelector('input[type="email"]', { timeout: 10000 });
    await page.type('input[type="email"]', '<EMAIL>');
    
    await page.waitForSelector('input[type="password"]', { timeout: 10000 });
    await page.type('input[type="password"]', 'J4913836j');
    
    // Click login button
    console.log('🔐 Clicking login button...');
    await page.click('button[type="submit"]');
    
    // Wait for navigation to dashboard
    console.log('⏳ Waiting for dashboard navigation...');
    await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 15000 });
    
    console.log('✅ Login successful! Current URL:', page.url());
    
    // Take screenshot of dashboard
    await page.screenshot({ path: '/tmp/dashboard_after_login.png', fullPage: true });
    console.log('📸 Dashboard screenshot saved');
    
    // Test sidebar navigation items
    const sidebarItems = [
      { name: 'Log Entry', url: '/dashboard/log' },
      { name: 'Progress', url: '/dashboard/progress' },
      { name: 'Goals', url: '/dashboard/goals' },
      { name: 'Breathing', url: '/dashboard/breathing' },
      { name: 'Focus', url: '/dashboard/focus' },
      { name: 'Mood', url: '/dashboard/mood' },
      { name: 'Rewards', url: '/dashboard/rewards' },
      { name: 'Health Data', url: '/dashboard/health-integrations' },
      { name: 'Journal', url: '/dashboard/journal' },
      { name: 'Community', url: '/dashboard/community' },
      { name: 'Learn', url: '/dashboard/learn' },
      { name: 'Support', url: '/dashboard/support' },
      { name: 'Settings', url: '/dashboard/settings' }
    ];
    
    console.log('🧭 Testing sidebar navigation items...');
    
    for (const item of sidebarItems) {
      console.log(`📍 Testing ${item.name} (${item.url})...`);
      
      // Navigate to the page
      await page.goto(`http://127.0.0.1:5003${item.url}`, { waitUntil: 'networkidle0' });
      
      // Take screenshot
      const filename = `/tmp/sidebar_test_${item.name.toLowerCase().replace(/\s+/g, '_')}.png`;
      await page.screenshot({ path: filename, fullPage: true });
      
      console.log(`📸 ${item.name} screenshot saved: ${filename}`);
      
      // Check for authentication issues
      const authRequired = await page.$('text=Authentication Required');
      const loginForm = await page.$('form[role="form"]');
      
      if (authRequired || loginForm) {
        console.log(`❌ ${item.name}: Authentication issue detected`);
      } else {
        console.log(`✅ ${item.name}: Page loaded successfully`);
      }
      
      // Wait a moment between tests
      await page.waitForTimeout(1000);
    }
    
    console.log('🎉 Sidebar navigation testing complete!');
    
    // Keep browser open for manual inspection
    console.log('🔍 Browser will remain open for 60 seconds for manual inspection...');
    await page.waitForTimeout(60000);
    
  } catch (error) {
    console.error('❌ Error during testing:', error);
    await page.screenshot({ path: '/tmp/error_screenshot.png', fullPage: true });
  } finally {
    await browser.close();
    console.log('🏁 Testing complete');
  }
}

// Run the test
loginAndTestSidebar().catch(console.error);
