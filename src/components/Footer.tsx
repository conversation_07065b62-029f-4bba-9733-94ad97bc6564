import { Link } from 'react-router-dom'
import Logo from './Logo'

export default function Footer() {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-12">
        <div className="grid md:grid-cols-5 gap-8">
          {/* Mission Fresh Brand */}
          <div>
            <div className="mb-4">
              <Logo size="md" showText={true} linkTo="/" className="text-primary-foreground hover:opacity-90" />
            </div>
            <p className="text-primary-foreground text-sm max-w-xs">
              A personalized journey to a smoke-free life, powered by AI and holistic wellness principles.
            </p>
          </div>

          {/* Resources Column */}
          <div>
            <h3 className="text-lg font-semibold mb-4">RESOURCES</h3>
            <nav className="space-y-2">
              <Link to="/tools/nrt-guide" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                NRT Guide
              </Link>
              <Link to="/tools/smokeless-directory" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                Smokeless Directory
              </Link>
              <Link to="/tools/quit-methods" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                Quit Methods
              </Link>
              <Link to="/tools/calculators" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                Calculators
              </Link>
            </nav>
          </div>

          {/* App Column */}
          <div>
            <h3 className="text-lg font-semibold mb-4">APP</h3>
            <nav className="space-y-2">
              <Link to="/features" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                Features
              </Link>
              <Link to="/dashboard" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                Dashboard
              </Link>
              <Link to="/progress" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                Progress
              </Link>
              <Link to="/community" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                Community
              </Link>
            </nav>
          </div>

          {/* Company Column */}
          <div>
            <h3 className="text-lg font-semibold mb-4">COMPANY</h3>
            <nav className="space-y-2">
              <Link to="/about" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                About
              </Link>
              <Link to="/privacy-policy" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                Privacy Policy
              </Link>
              <Link to="/terms-of-service" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                Terms of Service
              </Link>
              <Link to="/how-it-works" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                How it Works
              </Link>
            </nav>
          </div>

          {/* Support Column */}
          <div>
            <h3 className="text-lg font-semibold mb-4">SUPPORT</h3>
            <nav className="space-y-2">
              <Link to="/help-center" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                Help Center
              </Link>
              <Link to="/contact-us" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                Contact Us
              </Link>
              <Link to="/faq" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                FAQ
              </Link>
              <Link to="/feedback" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors no-underline min-h-[44px] py-2 flex items-center">
                Feedback
              </Link>
            </nav>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-primary-foreground/20 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-primary-foreground text-sm mb-4 md:mb-0">
              © 2025 Mission Fresh. All Rights Reserved.
            </p>
            <div className="flex space-x-4">
              <Link to="/contact-us" className="text-primary-foreground hover:opacity-90 min-h-[44px] min-w-[44px] flex items-center justify-center" aria-label="Contact us">
                <span className="sr-only">Contact</span>
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </Link>
              <Link to="/help-center" className="text-primary-foreground hover:opacity-90 min-h-[44px] min-w-[44px] flex items-center justify-center" aria-label="Help Center">
                <span className="sr-only">Help</span>
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </Link>
              <Link to="/feedback" className="text-primary-foreground hover:opacity-90 min-h-[44px] min-w-[44px] flex items-center justify-center" aria-label="Send Feedback">
                <span className="sr-only">Feedback</span>
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
