import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { Link } from 'react-router-dom';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to console for debugging
    console.error('Error Boundary caught an error:', error, errorInfo);
    
    // Update state with error details
    this.setState({
      error,
      errorInfo
    });

    // In production, you would send this to an error reporting service
    // Example: Sentry.captureException(error, { extra: errorInfo });
  }

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-6">
          <div className="max-w-md w-full text-center">
            <div className="bg-card border border-border rounded-xl p-8 shadow-lg">
              <div className="size-icon-large bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <AlertTriangle className="w-8 h-8 text-destructive" strokeWidth={1.5} />
              </div>
              
              <h1 className="text-2xl font-semibold text-foreground mb-4">
                Something went wrong
              </h1>
              
              <p className="text-muted-foreground mb-8 leading-relaxed">
                We're sorry, but something unexpected happened. Please try refreshing the page or return to the homepage.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={this.handleReload}
                  className="btn-primary min-h-[44px] px-6 py-3 rounded-lg font-medium flex items-center justify-center gap-2"
                  aria-label="Refresh the page"
                >
                  <RefreshCw className="w-4 h-4" strokeWidth={1.5} />
                  Refresh Page
                </button>
                
                <Link
                  to="/"
                  className="btn-secondary min-h-[44px] px-6 py-3 rounded-lg font-medium flex items-center justify-center gap-2"
                  aria-label="Go to homepage"
                >
                  <Home className="w-4 h-4" strokeWidth={1.5} />
                  Go Home
                </Link>
              </div>
              
              {/* Show error details in development */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-8 text-left">
                  <summary className="text-sm font-medium text-muted-foreground cursor-pointer hover:text-foreground">
                    Error Details (Development Only)
                  </summary>
                  <div className="mt-4 p-4 bg-muted rounded-lg text-xs font-mono text-muted-foreground overflow-auto">
                    <div className="mb-2">
                      <strong>Error:</strong> {this.state.error.message}
                    </div>
                    <div className="mb-2">
                      <strong>Stack:</strong>
                      <pre className="whitespace-pre-wrap mt-1">{this.state.error.stack}</pre>
                    </div>
                    {this.state.errorInfo && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="whitespace-pre-wrap mt-1">{this.state.errorInfo.componentStack}</pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
