import { BrowserRouter, Routes, Route } from 'react-router-dom'
import { Suspense, lazy } from 'react'
import Layout from './components/Layout'
import AppLayout from './components/AppLayout'
import { AuthProvider } from './contexts/AuthContext'

// Lazy load components for better performance
const LandingPage = lazy(() => import('./pages/LandingPage'))
const AuthPage = lazy(() => import('./pages/AuthPage'))
const ResetPasswordPage = lazy(() => import('./pages/ResetPasswordPage'))
const DashboardPage = lazy(() => import('./pages/DashboardPage'))
const ProgressPage = lazy(() => import('./pages/ProgressPage'))
const GoalsPage = lazy(() => import('./pages/GoalsPage'))
const BreathingPage = lazy(() => import('./pages/BreathingPage'))
const FocusPage = lazy(() => import('./pages/FocusPage'))
const MoodPage = lazy(() => import('./pages/MoodPage'))
const CommunityPage = lazy(() => import('./pages/CommunityPage'))
const LearnPage = lazy(() => import('./pages/LearnPage'))
const SettingsPage = lazy(() => import('./pages/SettingsPage'))
const HowItWorksPage = lazy(() => import('./pages/HowItWorksPage'))
const FeaturesPage = lazy(() => import('./pages/FeaturesPage'))
const ToolsPage = lazy(() => import('./pages/ToolsPage'))
const NRTGuidePage = lazy(() => import('./pages/NRTGuidePage'))
const SmokelessDirectoryPage = lazy(() => import('./pages/SmokelessDirectoryPage'))
const QuitMethodsPage = lazy(() => import('./pages/QuitMethodsPage'))
const NRTProductsPage = lazy(() => import('./pages/NRTProductsPage'))
const CalculatorsPage = lazy(() => import('./pages/CalculatorsPage'))
const HolisticHealthPage = lazy(() => import('./pages/HolisticHealthPage'))
const FreshAssistantPage = lazy(() => import('./pages/FreshAssistantPage'))
const AIAssistantPage = lazy(() => import('./pages/AIAssistantPage'))
const AccountPage = lazy(() => import('./pages/AccountPage'))
const LogEntryPage = lazy(() => import('./pages/LogEntryPage'))
const RewardsPage = lazy(() => import('./pages/RewardsPage'))
const HealthIntegrationsPage = lazy(() => import('./pages/HealthIntegrationsPage'))
const JournalPage = lazy(() => import('./pages/JournalPage'))
const SupportPage = lazy(() => import('./pages/SupportPage'))
const BreathingToolsPage = lazy(() => import('./pages/BreathingToolsPage'))
const SearchPage = lazy(() => import('./pages/SearchPage'))
const AboutPage = lazy(() => import('./pages/AboutPage'))
const PrivacyPolicyPage = lazy(() => import('./pages/PrivacyPolicyPage'))
const TermsOfServicePage = lazy(() => import('./pages/TermsOfServicePage'))
const HelpCenterPage = lazy(() => import('./pages/HelpCenterPage'))
const ContactUsPage = lazy(() => import('./pages/ContactUsPage'))
const FAQPage = lazy(() => import('./pages/FAQPage'))
const FeedbackPage = lazy(() => import('./pages/FeedbackPage'))

// Loading component for Suspense fallback
const PageLoader = () => (
  <div className="min-h-screen bg-background flex items-center justify-center">
    <div className="flex flex-col items-center gap-4">
      <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
      <p className="text-muted-foreground font-medium">Loading...</p>
    </div>
  </div>
)

export default function Router() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <Suspense fallback={<PageLoader />}>
          <Routes>
          {/* Standalone auth page without layout */}
          <Route path="auth" element={<AuthPage />} />
          <Route path="reset-password" element={<ResetPasswordPage />} />

          <Route path="/" element={<Layout />}>
            <Route index element={<LandingPage />} />
            <Route path="how-it-works" element={<HowItWorksPage />} />
            <Route path="features" element={<FeaturesPage />} />
            <Route path="tools" element={<ToolsPage />} />
            <Route path="tools/nrt-guide" element={<NRTGuidePage />} />
            <Route path="tools/nrt-products" element={<NRTProductsPage />} />
            <Route path="tools/smokeless-directory" element={<SmokelessDirectoryPage />} />
            <Route path="tools/quit-methods" element={<QuitMethodsPage />} />
            <Route path="quit-methods" element={<QuitMethodsPage />} />
            <Route path="tools/calculators" element={<CalculatorsPage />} />
            <Route path="tools/holistic-health" element={<HolisticHealthPage />} />
            <Route path="fresh-assistant" element={<FreshAssistantPage />} />
            <Route path="ai-assistant" element={<AIAssistantPage />} />
            <Route path="search" element={<SearchPage />} />
            <Route path="account" element={<AccountPage />} />
            <Route path="about" element={<AboutPage />} />
            <Route path="privacy-policy" element={<PrivacyPolicyPage />} />
            <Route path="terms-of-service" element={<TermsOfServicePage />} />
            <Route path="help-center" element={<HelpCenterPage />} />
            <Route path="contact-us" element={<ContactUsPage />} />
            <Route path="faq" element={<FAQPage />} />
            <Route path="feedback" element={<FeedbackPage />} />
          </Route>
          <Route path="dashboard" element={<AppLayout />}>
            <Route index element={<DashboardPage />} />
            <Route path="progress" element={<ProgressPage />} />
            <Route path="goals" element={<GoalsPage />} />
            <Route path="log" element={<LogEntryPage />} />
            <Route path="rewards" element={<RewardsPage />} />
            <Route path="breathing" element={<BreathingPage />} />
            <Route path="focus" element={<FocusPage />} />
            <Route path="mood" element={<MoodPage />} />
            <Route path="community" element={<CommunityPage />} />
            <Route path="learn" element={<LearnPage />} />
            <Route path="settings" element={<SettingsPage />} />
            <Route path="health-integrations" element={<HealthIntegrationsPage />} />
            <Route path="journal" element={<JournalPage />} />
            <Route path="support" element={<SupportPage />} />
          </Route>
          </Routes>
        </Suspense>
      </AuthProvider>
    </BrowserRouter>
  )
}
