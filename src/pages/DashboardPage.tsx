import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Calendar, Shield, Wallet, Heart, BarChart, Target, BookOpen, AlertCircle, Activity, Users, RefreshCw, TrendingUp, Award, Sparkles } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { Link } from 'react-router-dom'
import InlineLoginForm from '../components/InlineLoginForm'
import ErrorBoundary from '../components/ErrorBoundary'

// Health calculation constants
const MINUTES_LIFE_LOST_PER_CIGARETTE = 11 // Based on medical research: average life lost per cigarette

// Currency formatting utility
const formatCurrency = (amount: number, locale: string = 'en-US', currency: string = 'USD') => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

// Memoized StatCard component for performance
const MemoizedStatCard = React.memo(StatCard)

// Memoized QuickActionCard component for performance
const MemoizedQuickActionCard = React.memo(QuickActionCard)

interface StatCardProps {
  icon: React.ElementType
  title: string
  value: string | number
  description: string
  color?: string
}

function StatCard({ icon: Icon, title, value, description }: StatCardProps) {
  const formattedValue = typeof value === 'number' ? value.toLocaleString() : value

  return (
    <div
      className="stats-card rounded-xl p-10 shadow-lg transition-all duration-300 hover:scale-[1.02] focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
      role="article"
      aria-labelledby={`stat-${title.toLowerCase().replace(/\s+/g, '-')}`}
      tabIndex={0}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div
            id={`stat-${title.toLowerCase().replace(/\s+/g, '-')}`}
            className="text-sm font-semibold text-muted-foreground mb-3 tracking-wide uppercase"
          >
            {title}
          </div>
          <div
            className="text-4xl font-bold text-foreground mb-2 tracking-tight"
            aria-label={`${title}: ${formattedValue}`}
          >
            {formattedValue}
          </div>
          <div className="text-sm text-muted-foreground leading-relaxed">{description}</div>
        </div>
        <div className="w-14 h-14 bg-primary rounded-xl flex items-center justify-center shadow-lg">
          <Icon className="w-7 h-7 text-primary-foreground" strokeWidth={2} aria-hidden="true" />
        </div>
      </div>
    </div>
  )
}

function QuickActionCard({ icon: Icon, title, description, href }: {
  icon: React.ElementType
  title: string
  description: string
  href: string
}) {
  return (
    <Link
      to={href}
      className="quick-action-button rounded-lg p-6 min-h-[56px] shadow-sm transition-all duration-300 group hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
      aria-label={`${title}: ${description}`}
      role="button"
    >
      <div className="flex items-center gap-4">
        <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center group-hover:scale-105 transition-all duration-300 shadow-lg">
          <Icon className="w-6 h-6 text-primary-foreground" strokeWidth={2} aria-hidden="true" />
        </div>
        <div className="flex-1">
          <div className="font-semibold text-card-foreground mb-1 group-hover:text-primary transition-colors">{title}</div>
          <div className="text-sm text-muted-foreground leading-relaxed">{description}</div>
        </div>
      </div>
    </Link>
  )
}

export default function DashboardPage() {
  const { user } = useAuth()
  const [stats, setStats] = useState({
    daysQuit: 0,
    moneySaved: 0,
    cigarettesAvoided: 0,
    lifeRegained: 0
  })
  const [hasGoal, setHasGoal] = useState(false)
  const [dailyLogs, setDailyLogs] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingStats, setIsLoadingStats] = useState(true)
  const [isLoadingLogs, setIsLoadingLogs] = useState(true)
  const [error, setError] = useState('')
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Memoized stats calculation to prevent unnecessary recalculations
  const calculatedStats = useMemo(() => {
    if (!hasGoal || !stats.daysQuit) {
      return { daysQuit: 0, moneySaved: 0, cigarettesAvoided: 0, lifeRegained: 0 }
    }

    return stats
  }, [hasGoal, stats.daysQuit, stats.moneySaved, stats.cigarettesAvoided, stats.lifeRegained])

  // Memoized currency formatting to prevent recreation on every render
  const memoizedFormatCurrency = useCallback((amount: number) => {
    return formatCurrency(amount)
  }, [])

  // Memoized data loading function for performance
  const loadDashboardData = useCallback(async () => {
    if (!user) return

    try {
      setIsLoading(true)
      setIsLoadingStats(true)
      setIsLoadingLogs(true)

      // Load user's goal data - get the most recent goal
      let goalData = null

      try {
        // Get the most recent goal (no status field in table)
        const { data: recentGoalData, error: recentGoalError } = await supabase
          .from('user_goals')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle()

        if (!recentGoalError && recentGoalData) {
          goalData = recentGoalData
        }
      } catch (goalError) {
        console.error('Error loading goal data:', goalError)
      }

      setHasGoal(!!goalData)

      // Calculate stats if user has a goal
      if (goalData && goalData.quit_date) {
        const quitDate = new Date(goalData.quit_date)
        const today = new Date()
        const daysQuit = Math.max(0, Math.floor((today.getTime() - quitDate.getTime()) / (1000 * 60 * 60 * 24)))

        const dailyUsage = goalData.typical_daily_usage || 0
        const costPerUnit = goalData.cost_per_unit || 0

        setStats({
          daysQuit,
          moneySaved: Math.round(daysQuit * dailyUsage * costPerUnit),
          cigarettesAvoided: Math.round(daysQuit * dailyUsage),
          lifeRegained: Math.round((daysQuit * dailyUsage * MINUTES_LIFE_LOST_PER_CIGARETTE) / 60) // Convert minutes to hours
        })
      }
      setIsLoadingStats(false)

      // Try to load daily logs count - handle table not existing
      let logsCount = 0
      try {
        const { data: logsData, error: logsError } = await supabase
          .from('daily_health_summaries')
          .select('id')
          .eq('user_id', user.id)

        if (!logsError && logsData) {
          logsCount = logsData.length
        }
      } catch (logsError) {
        // Daily health summaries table not available - this is expected for new users
      }

      setDailyLogs(logsCount)
      setIsLoadingLogs(false)

      // Update last refreshed timestamp
      setLastUpdated(new Date())

    } catch (error) {
      console.error('Error loading dashboard data:', error)
      setError('Failed to load dashboard data. Please refresh the page.')
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }, [user])

  // Memoized manual refresh function
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    setError('')
    await loadDashboardData()
  }, [loadDashboardData])

  // Add meta tags for SEO
  React.useEffect(() => {
    document.title = 'Dashboard - Mission Fresh Lite | Track Your Wellness Journey'

    // Add meta description
    let metaDescription = document.querySelector('meta[name="description"]')
    if (!metaDescription) {
      metaDescription = document.createElement('meta')
      metaDescription.setAttribute('name', 'description')
      document.head.appendChild(metaDescription)
    }
    metaDescription.setAttribute('content', 'Track your smoking cessation progress, view statistics, and manage your wellness goals on your personal Mission Fresh dashboard.')

    // Add canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]')
    if (!canonicalLink) {
      canonicalLink = document.createElement('link')
      canonicalLink.setAttribute('rel', 'canonical')
      document.head.appendChild(canonicalLink)
    }
    canonicalLink.setAttribute('href', `${window.location.origin}/dashboard`)
  }, [])

  useEffect(() => {
    // RULE 0001: Only proceed if real user is authenticated
    if (!user) {
      setIsLoading(false)
      setError('Please log in to view your dashboard')
      return
    }



    loadDashboardData()
  }, [user?.id]) // FIX: Use user.id instead of entire user object to prevent infinite loop

  // Auto-refresh every 5 minutes
  useEffect(() => {
    if (!user) return

    const interval = setInterval(() => {
      loadDashboardData()
    }, 5 * 60 * 1000) // 5 minutes

    return () => clearInterval(interval)
  }, [user?.id])

  // HOLY RULE #1: Enforce authentication - show inline login form for better UX
  if (!user) {
    return (
      <InlineLoginForm
        title="Access Your Dashboard"
        subtitle="Sign in to view your wellness progress and continue your journey"
      />
    )
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Breadcrumb Navigation */}
      <nav aria-label="Breadcrumb" className="bg-background/80 backdrop-blur-sm border-b border-border">
        <div className="container mx-auto px-4 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a href="/" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Home
              </a>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <span className="text-foreground font-medium" aria-current="page">
                Dashboard
              </span>
            </li>
          </ol>
        </div>
      </nav>

      {/* Skip navigation links - hidden by default, only visible on keyboard focus */}
      <a
        href="#dashboard-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-20 focus:left-4 bg-primary text-primary-foreground px-4 py-3 min-h-[44px] min-w-[44px] flex items-center justify-center rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-primary-foreground"
      >
        Skip to dashboard content
      </a>

      {/* ARIA live region for dynamic updates */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {error && `Error: ${error}`}
        {isLoading && 'Loading dashboard data...'}
        {lastUpdated && `Dashboard updated at ${lastUpdated.toLocaleTimeString()}`}
      </div>

      <main role="main" aria-label="Mission Fresh Dashboard" className="py-8">
        <div className="space-y-8 max-w-7xl mx-auto px-6 lg:px-8">
          {/* Header */}
          <header className="mb-12" role="banner">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="dashboard-title text-4xl mb-4 tracking-tight" id="dashboard-main-heading">
                  Dashboard
                </h1>
                <p className="dashboard-subtitle text-xl leading-relaxed" aria-describedby="dashboard-main-heading">
                  Track your progress and stay motivated on your wellness journey
                </p>
              </div>
              <div className="flex items-center gap-4">
                {lastUpdated && (
                  <span className="text-sm text-muted-foreground">
                    Last updated: {lastUpdated.toLocaleTimeString()}
                  </span>
                )}
                <button
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="btn-secondary inline-flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  aria-label="Refresh dashboard data"
                >
                  <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} strokeWidth={2} />
                  {isRefreshing ? 'Refreshing...' : 'Refresh'}
                </button>
              </div>
            </div>
          </header>

          <div id="dashboard-content">
            {/* Error Display */}
            {error && (
              <div className="bg-muted border border-border rounded-xl p-6 mb-8" role="alert" aria-live="polite">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center shadow-lg">
                    <AlertCircle className="w-5 h-5 text-primary-foreground" strokeWidth={2} />
                  </div>
                  <div>
                    <h3 className="font-bold text-foreground mb-1">Error Loading Dashboard</h3>
                    <p className="text-muted-foreground">{error}</p>
                  </div>
                </div>
              </div>
            )}

      {/* Stats Grid */}
      <ErrorBoundary>
        <section id="dashboard-stats" aria-labelledby="stats-heading">
          <h2 id="stats-heading" className="text-2xl font-bold text-foreground mb-8 flex items-center gap-3">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <BarChart className="w-5 h-5 text-primary-foreground" strokeWidth={2} />
            </div>
            Your Progress Statistics
          </h2>
          {isLoadingStats ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12" role="region" aria-label="Loading progress statistics">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="stats-card rounded-xl p-10 shadow-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="h-4 bg-muted rounded mb-3 animate-pulse"></div>
                      <div className="h-8 bg-muted rounded mb-2 animate-pulse"></div>
                      <div className="h-3 bg-muted rounded animate-pulse"></div>
                    </div>
                    <div className="w-14 h-14 bg-muted rounded-xl animate-pulse"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : hasGoal ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12" role="region" aria-label="Progress statistics">
              <MemoizedStatCard
                icon={Calendar}
                title="Smoke-Free Days"
                value={calculatedStats.daysQuit}
                description={`${calculatedStats.daysQuit} consecutive days clean`}
              />
              <MemoizedStatCard
                icon={Wallet}
                title="Money Saved"
                value={memoizedFormatCurrency(calculatedStats.moneySaved)}
                description={`${memoizedFormatCurrency(calculatedStats.moneySaved)} total savings`}
              />
              <MemoizedStatCard
                icon={Shield}
                title="Cigarettes Avoided"
                value={calculatedStats.cigarettesAvoided}
                description={`${calculatedStats.cigarettesAvoided.toLocaleString()} healthier choices`}
              />
              <MemoizedStatCard
                icon={Heart}
                title="Life Regained"
                value={`${calculatedStats.lifeRegained} hours`}
                description={`${calculatedStats.lifeRegained} hours of life regained`}
              />
            </div>
          ) : (
          <div className="bg-muted border border-border rounded-xl p-12 mb-12 text-center">
            <div className="max-w-md mx-auto">
              {/* Illustration with multiple icons */}
              <div className="relative mb-8">
                <div className="w-24 h-24 bg-primary rounded-2xl flex items-center justify-center mx-auto shadow-lg">
                  <Target className="w-12 h-12 text-primary-foreground" strokeWidth={2} />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-accent rounded-full flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-accent-foreground" strokeWidth={2} />
                </div>
                <div className="absolute -bottom-2 -left-2 w-8 h-8 bg-secondary rounded-full flex items-center justify-center">
                  <TrendingUp className="w-4 h-4 text-secondary-foreground" strokeWidth={2} />
                </div>
              </div>

              <h3 className="text-2xl font-bold text-foreground mb-4">Ready to Start Your Journey?</h3>
              <p className="text-muted-foreground mb-8 leading-relaxed text-lg">
                Set your first wellness goal to unlock personalized progress tracking, motivational insights, and celebrate your achievements along the way.
              </p>

              <div className="space-y-4">
                <Link
                  to="/dashboard/goals"
                  className="btn-primary inline-flex items-center gap-3 px-8 py-4 rounded-xl transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl"
                >
                  <Target className="w-6 h-6" strokeWidth={2} />
                  Set Your First Goal
                </Link>

                <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Award className="w-4 h-4" strokeWidth={2} />
                    Track Progress
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-4 h-4" strokeWidth={2} />
                    See Improvements
                  </div>
                  <div className="flex items-center gap-2">
                    <Sparkles className="w-4 h-4" strokeWidth={2} />
                    Stay Motivated
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        </section>
      </ErrorBoundary>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Progress Overview */}
        <ErrorBoundary>
          <div className="lg:col-span-2 dashboard-card rounded-xl shadow-sm p-8" role="region" aria-labelledby="progress-overview-heading">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-card-foreground flex items-center gap-3" id="progress-overview-heading">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center shadow-lg">
                <BarChart className="w-5 h-5 text-primary-foreground" strokeWidth={2} />
              </div>
              Progress Overview
            </h3>
            <Link
              to="/dashboard/progress"
              className="text-sm text-primary hover:text-primary-hover font-semibold transition-colors min-h-[44px] py-2 px-3 flex items-center"
              aria-label="View detailed progress information"
            >
              View Details →
            </Link>
          </div>
          <div className="bg-muted rounded-xl h-80 p-6 border border-border">
            {hasGoal ? (
              <div className="h-full flex flex-col">
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-foreground mb-1">{stats.daysQuit}</div>
                    <div className="text-sm text-muted-foreground">Days Smoke-Free</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-foreground mb-1">{memoizedFormatCurrency(calculatedStats.moneySaved)}</div>
                    <div className="text-sm text-muted-foreground">Money Saved</div>
                  </div>
                </div>
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <div className="progress-circle w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <span className="text-2xl font-bold text-primary-foreground">{stats.daysQuit}</span>
                    </div>
                    <p className="text-sm text-muted-foreground font-medium">Days on your journey</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-foreground mb-1">{stats.cigarettesAvoided}</div>
                    <div className="text-xs text-muted-foreground">Cigarettes Avoided</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-foreground mb-1">{stats.lifeRegained}h</div>
                    <div className="text-xs text-muted-foreground">Life Regained</div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center py-16">
                <div className="text-center max-w-sm">
                  <div className="relative mb-6">
                    <div className="w-20 h-20 bg-primary rounded-2xl flex items-center justify-center mx-auto shadow-lg">
                      <BarChart className="w-10 h-10 text-primary-foreground" strokeWidth={2} />
                    </div>
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-accent rounded-full flex items-center justify-center">
                      <Sparkles className="w-3 h-3 text-accent-foreground" strokeWidth={2} />
                    </div>
                  </div>
                  <h4 className="text-lg font-bold text-foreground mb-2">Your Progress Awaits</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Once you set your wellness goal, this space will come alive with beautiful charts and insights tracking your amazing journey.
                  </p>
                </div>
              </div>
            )}
          </div>
          </div>
        </ErrorBoundary>

        {/* Quick Actions */}
        <ErrorBoundary>
          <section id="quick-actions" className="space-y-8" aria-labelledby="quick-actions-heading">
          <div className="dashboard-card rounded-xl shadow-sm p-8">
            <h3 className="text-2xl font-bold text-card-foreground mb-6 flex items-center gap-3" id="quick-actions-heading">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Activity className="w-5 h-5 text-primary-foreground" strokeWidth={2} />
              </div>
              Quick Actions
            </h3>
            <div className="space-y-4">
              <MemoizedQuickActionCard
                icon={BookOpen}
                title="Log Daily Entry"
                description="Track your wellness journey"
                href="/dashboard/log"
              />
              <MemoizedQuickActionCard
                icon={Heart}
                title="Check Mood"
                description="Monitor your emotional state"
                href="/dashboard/mood"
              />
              <MemoizedQuickActionCard
                icon={Target}
                title="Update Goals"
                description="Adjust your targets"
                href="/dashboard/goals"
              />
              <MemoizedQuickActionCard
                icon={Users}
                title="Community"
                description="Connect with others"
                href="/dashboard/community"
              />
            </div>
          </div>

          {/* Activity Summary */}
          <div className="dashboard-card rounded-xl shadow-sm p-8" role="region" aria-labelledby="activity-summary-heading">
            <h3 className="text-2xl font-bold text-card-foreground mb-6 flex items-center gap-3" id="activity-summary-heading">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Activity className="w-5 h-5 text-primary-foreground" strokeWidth={2} />
              </div>
              Activity Summary
            </h3>
            {isLoadingLogs ? (
              <div className="space-y-5" role="list" aria-label="Loading activity statistics">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="activity-item flex items-center justify-between py-3 min-h-[44px]" role="listitem">
                    <div className="h-4 bg-muted rounded animate-pulse w-32"></div>
                    <div className="h-6 bg-muted rounded animate-pulse w-16"></div>
                  </div>
                ))}
              </div>
            ) : dailyLogs === 0 && !hasGoal ? (
              <div className="text-center py-12">
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mx-auto shadow-lg">
                    <Activity className="w-8 h-8 text-primary-foreground" strokeWidth={2} />
                  </div>
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-accent rounded-full flex items-center justify-center">
                    <Sparkles className="w-2.5 h-2.5 text-accent-foreground" strokeWidth={2} />
                  </div>
                </div>
                <h4 className="text-lg font-bold text-foreground mb-2">Start Tracking Today</h4>
                <p className="text-muted-foreground text-sm leading-relaxed mb-6">
                  Your activity summary will show here once you begin logging your wellness journey.
                </p>
                <Link
                  to="/dashboard/log"
                  className="btn-secondary inline-flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 font-medium"
                >
                  <BookOpen className="w-4 h-4" strokeWidth={2} />
                  Create First Log
                </Link>
              </div>
            ) : (
              <div className="space-y-5" role="list" aria-label="Activity statistics">
                <div className="activity-item flex items-center justify-between py-3 min-h-[44px]" role="listitem">
                  <span className="text-muted-foreground font-medium">Daily Logs Completed</span>
                  <span className="font-bold text-foreground text-lg" aria-label={`${dailyLogs} daily logs completed`}>
                    {dailyLogs.toLocaleString()}
                  </span>
                </div>
                <div className="activity-item flex items-center justify-between py-3 min-h-[44px]" role="listitem">
                  <span className="text-muted-foreground font-medium">Current Streak</span>
                  <span className="font-bold text-foreground text-lg" aria-label={`Current streak: ${hasGoal ? stats.daysQuit : 0} days`}>
                    {(hasGoal ? stats.daysQuit : 0).toLocaleString()} days
                  </span>
                </div>
                <div className="flex items-center justify-between py-3 border-b border-border last:border-b-0">
                  <span className="text-muted-foreground font-medium">Goal Status</span>
                  <span className={`font-bold text-lg ${hasGoal ? 'text-primary' : 'text-muted-foreground'}`}>
                    {hasGoal ? 'Active' : 'Not Set'}
                  </span>
                </div>
              </div>
            )}
          </div>
          </section>
        </ErrorBoundary>
        </div>
          </div>
        </div>
      </main>
    </div>
  )
}
