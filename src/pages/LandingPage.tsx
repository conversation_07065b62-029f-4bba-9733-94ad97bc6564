import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>les,
  Eye,
  Bot,
  Send,
  TrendingUp,
  Star,
  Shield,
  Search
} from 'lucide-react'
import { useState, useCallback, useEffect, useMemo } from 'react'
import { motion } from 'framer-motion'
import { Link, useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'

// HOLY RULE 0001: Testimonials interface for database data from community_posts
interface Testimonial {
  id: string;
  title: string;
  content: string;
  likes_count: number;
  created_at: string;
}

// RULE 0001: Dynamic conversation starters from database - hardcoded array removed
// Conversation starters will be loaded from conversation_starters table

// AI Message interface
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  suggestions?: string[];
}


// Local AI response generator - database only, no hardcoded fallbacks
const generateLocalAIResponse = async (): Promise<string | null> => {
  try {
    const { data: responses, error } = await supabase
      .from('ai_responses')
      .select('response_text')
      .eq('active', true)
      .order('sort_order');

    if (error) throw error;

    if (responses && responses.length > 0) {
      const randomIndex = Math.floor(Math.random() * responses.length);
      return responses[randomIndex].response_text;
    }

    // No fallback - if no responses in database, return null to indicate failure
    return null;
  } catch (error) {
    console.error('Error fetching AI responses:', error);
    return null;
  }
};

// AI Coach service integration
const getAICoachResponse = async (
  userMessage: string,
  conversationHistory?: Array<{ role: string; content: string }>
): Promise<string | null> => {
  if (!userMessage || typeof userMessage !== 'string') {
    return "I'd love to help you! Please share what's on your mind about your quit journey.";
  }

  try {
    // Use direct Gemini API
    const prompt = `You are "FreshAI", a friendly, empathetic, and highly knowledgeable AI quit smoking coach. Your goal is to support users in their journey to quit nicotine. Be encouraging, provide practical advice, and help users explore their feelings and triggers. Keep responses concise and actionable (under 200 words). Avoid making medical claims or giving medical advice; instead, suggest users consult healthcare professionals for medical concerns.

Current user message:
"${userMessage}"

Conversation context (if any):`;

    let fullPrompt = prompt;
    if (conversationHistory && conversationHistory.length > 0) {
      const historySnippet = conversationHistory
        .slice(-4)
        .map(msg => `${msg.role === 'user' ? 'User' : 'Coach'}: ${msg.content}`)
        .join('\n');
      fullPrompt += `\n${historySnippet}`;
    } else {
      fullPrompt += "\nNo previous conversation context for this session.";
    }

    fullPrompt += `\n\nPlease provide a supportive, encouraging response as FreshAI, the quit smoking coach. Be warm, understanding, and offer practical advice.`;



    // Use Supabase edge function that securely handles Gemini API
    const { data, error } = await supabase.functions.invoke('get-gemini-suggestions', {
      body: {
        user_input: userMessage,
        user_id: 'anonymous' // For non-authenticated users
      }
    });

    if (error) {

      return await generateLocalAIResponse();
    }

    if (data?.suggestion) {

      return data.suggestion;
    } else {

      return await generateLocalAIResponse();
    }

  } catch (error) {

    // Use local AI response generator as fallback
    return await generateLocalAIResponse();
  }
};

export default function LandingPage() {
  const navigate = useNavigate();

  // AI Chat state directly in component
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showChat, setShowChat] = useState(false);

  // HOLY RULE #12: Conversation starters are static UI elements, hardcoded
  const conversationStarters = [
    "How do I start my quit journey?",
    "What are the best methods to quit smoking?",
    "How can I manage cravings and withdrawal?"
  ];
  const [searchQuery, setSearchQuery] = useState('');

  // Set page title and meta description for homepage
  useEffect(() => {
    document.title = 'Mission Fresh - Your Complete Quit Smoking Journey Companion';

    // Update meta description (optimized for SEO - under 160 chars)
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', 'Get instant, personalized support for your quit smoking journey. Access AI coaching and evidence-based wellness tools.');
    }

    // Add structured data for better SEO
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "Mission Fresh",
      "description": "Complete quit smoking journey companion with AI coaching and evidence-based wellness tools",
      "url": "https://missionfresh.com",
      "applicationCategory": "HealthApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "featureList": [
        "AI-powered quit smoking coach",
        "Nicotine replacement therapy guide",
        "Progress tracking and analytics",
        "Community support and testimonials",
        "Wellness tools and calculators"
      ]
    };

    // Remove existing structured data script if present
    const existingScript = document.querySelector('script[type="application/ld+json"]');
    if (existingScript) {
      existingScript.remove();
    }

    // Add new structured data script
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);
  }, []);

  // HOLY RULE 0001: Dynamic testimonials from database - NO HARDCODED DATA
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [isLoadingTestimonials, setIsLoadingTestimonials] = useState(true);



  // Memoized values for performance optimization
  const displayedStarters = useMemo(() =>
    conversationStarters.slice(0, 3),
    [conversationStarters]
  );

  const loadingSkeletons = useMemo(() =>
    Array.from({ length: 3 }).map((_, index) => (
      <div
        key={index}
        className="w-full size-button bg-muted animate-pulse rounded-lg"
      />
    )),
    []
  );



  // HOLY RULE #12: Conversation starters are static UI elements - no database loading needed

  // HOLY RULE 0001: Load testimonials from database - NO HARDCODED DATA
  useEffect(() => {
    const loadTestimonials = async () => {
      try {
        setIsLoadingTestimonials(true);
        const { data, error } = await supabase
          .from('community_posts')
          .select('id, content, title, likes_count, created_at')
          .eq('post_type', 'testimonial')
          .order('created_at', { ascending: false })
          .limit(3);

        if (error) {
          console.error('Error loading testimonials:', error);
          setTestimonials([]);
        } else {
          setTestimonials(data || []);
        }
      } catch (error) {
        console.error('Error loading testimonials:', error);
        setTestimonials([]);
      } finally {
        setIsLoadingTestimonials(false);
      }
    };

    loadTestimonials();
  }, []);

  const handleSendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: message,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');

    try {
      // Get conversation history from current messages state
      const conversationHistory = messages.map(m => ({
        role: m.sender === 'user' ? 'user' : 'assistant',
        content: m.content
      }));

      // Call AI response directly without nested state updates
      const aiResponseText = await getAICoachResponse(message, conversationHistory);

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: aiResponseText || "I'm here to help with your quit smoking journey! How can I support you today?",
        sender: 'ai',
        timestamp: new Date(),
        suggestions: displayedStarters
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error calling AI coach:', error);
      // Provide helpful fallback response
      const fallbackMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "I'm here to support your quit smoking journey! I can help with cravings, withdrawal symptoms, motivation, and more. What would you like to talk about?",
        sender: 'ai',
        timestamp: new Date(),
        suggestions: displayedStarters
      };
      setMessages(prev => [...prev, fallbackMessage]);
    }
  }, [displayedStarters]);



  return (
    <div className="min-h-screen">

      {/* Hero Section */}
      <section id="main-content" className="relative py-20 lg:py-32 bg-background">
        <div className="container mx-auto px-8 flex flex-col lg:flex-row items-stretch gap-12 lg:gap-16">
          {/* Left Column - Hero Content */}
          <div className="w-full lg:w-2/3">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-[1.1] tracking-tight mb-8 text-center lg:text-left">
                Take the first step
                <span className="block font-semibold text-primary mt-2">to a smoke-free future</span>
              </h1>

              <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto lg:mx-0 mb-8 text-center lg:text-left font-normal tracking-tight">
                Get instant, personalized support for your quit smoking journey.
                Our Fresh Assistant provides proven strategies and motivates you
                every step of the way.
              </p>

              {/* Search Component */}
              <div className="mb-8">
                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    if (searchQuery.trim()) {
                      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
                    }
                  }}
                  role="search"
                  aria-label="Search tools and methods"
                >
                  <label htmlFor="homepage-search" className="sr-only">
                    Search tools, methods, or ask a question
                  </label>
                  <div className="relative max-w-md lg:max-w-lg w-full">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" strokeWidth={1.5} />
                    <input
                      id="homepage-search"
                      type="search"
                      placeholder="Search tools, methods, or ask a question"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={async (e) => {
                        if (e.key === 'Enter' && searchQuery.trim()) {
                          // Start Fresh Assistant chat with user input
                          setShowChat(true);
                          setIsTyping(true);
                          
                          // Add user message to chat
                          const userMessage: Message = {
                            id: Date.now().toString(),
                            content: searchQuery.trim(),
                            sender: 'user',
                            timestamp: new Date()
                          };
                          setMessages(prev => [...prev, userMessage]);
                          
                          // Clear input
                          setSearchQuery('');
                          
                          // Get AI response
                          try {
                            const aiResponse = await getAICoachResponse(searchQuery.trim());
                            const aiMessage: Message = {
                              id: (Date.now() + 1).toString(),
                              content: aiResponse || "I'm here to help you with your quit smoking journey. How can I support you today?",
                              sender: 'ai',
                              timestamp: new Date()
                            };
                            setMessages(prev => [...prev, aiMessage]);
                          } catch (error) {
                            console.error('Error getting AI response:', error);
                            const errorMessage: Message = {
                              id: (Date.now() + 1).toString(),
                              content: "I'm here to help you with your quit smoking journey. How can I support you today?",
                              sender: 'ai',
                              timestamp: new Date()
                            };
                            setMessages(prev => [...prev, errorMessage]);
                          } finally {
                            setIsTyping(false);
                          }
                        }
                      }}
                      aria-label="Search tools, methods, or ask a question"
                      required
                      minLength={2}
                      className="w-full size-input pl-12 pr-4 text-lg border border-border bg-background text-foreground placeholder:text-muted-foreground/70 focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary rounded-lg shadow-sm transition-all duration-200 hover:border-primary/50"
                    />
                  </div>
                </form>
              </div>

              <div className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start mt-8">
                <Link
                  to="/auth?action=signup"
                  className="group font-semibold min-w-[220px] min-h-[44px] size-button-large text-lg bg-primary text-primary-foreground flex items-center justify-center gap-3 rounded-xl px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-200 hover:bg-primary/95 hover:-translate-y-0.5"
                  aria-label="Start your smoke-free journey by signing up"
                  role="button"
                >
                  <Sparkles className="w-5 h-5" strokeWidth={1.5} aria-hidden="true" />
                  <span className="tracking-[-0.01em]">Start Your Journey</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" strokeWidth={1.5} aria-hidden="true" />
                </Link>

                <Link
                  to="/how-it-works"
                  className="group font-semibold min-w-[220px] min-h-[44px] size-button-large text-lg border border-border bg-background text-foreground flex items-center justify-center gap-3 rounded-xl px-8 py-3 shadow-sm transition-colors duration-150 hover:border-primary hover:bg-accent/50 hover:shadow-md"
                  aria-label="Learn how Mission Fresh works"
                >
                  <Eye className="w-5 h-5" strokeWidth={1.5} aria-hidden="true" />
                  <span className="tracking-[-0.01em]">Learn How It Works</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" strokeWidth={1.5} aria-hidden="true" />
                </Link>
              </div>
            </motion.div>
          </div>

          {/* Right Column - AI Chat */}
          <div className="w-full lg:w-1/3 lg:self-start mt-8 lg:mt-4">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1, duration: 0.6 }}
            >
              <div className="w-full h-auto bg-background rounded-xl shadow-md hover:shadow-lg transition-all duration-200">
                <div className="p-6 bg-background rounded-t-xl">
                  <div className="flex items-center gap-4">
                    <div className="size-avatar bg-primary flex items-center justify-center border border-primary rounded-2xl">
                      <Bot className="w-5 h-5 text-primary-foreground" strokeWidth={1.5} />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-foreground tracking-tight">Fresh Assistant</h2>
                      <p className="text-muted-foreground text-sm font-medium tracking-tight">
                        Your 24/7 wellness coach
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-6 space-y-4">
                  {showChat && messages.length > 0 ? (
                    // Chat messages display
                    <div className="space-y-4 max-h-80 overflow-y-auto">
                      {messages.map((message) => (
                        <div key={message.id} className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                          <div className={`max-w-[80%] p-3 rounded-lg ${
                            message.sender === 'user'
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-muted text-foreground'
                          }`}>
                            <p className="text-sm">{message.content}</p>
                          </div>
                        </div>
                      ))}
                      {isTyping && (
                        <div className="flex justify-start">
                          <div className="bg-muted text-foreground p-3 rounded-lg">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                              <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce [animation-delay:0.1s]"></div>
                              <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce [animation-delay:0.2s]"></div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <>
                      <p className="text-sm font-medium text-muted-foreground tracking-tight">Start a conversation:</p>
                      <div className="flex flex-col gap-4 w-full">
                    {false ? (
                      // Loading state for conversation starters
                      loadingSkeletons
                    ) : (
                      displayedStarters.map((starter) => (
                        <button
                          key={starter}
                          className="btn-secondary w-full size-button text-base font-medium text-left justify-start px-5 py-3 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-0.5"
                          onClick={() => {
                            handleSendMessage(starter);
                          }}
                          aria-label={`Start conversation with: ${starter}`}
                          role="button"
                          tabIndex={0}
                        >
                          {starter}
                        </button>
                      ))
                    )}
                      </div>
                    </>
                  )}

                  <div className="mt-6 pt-6 border-t border-border">
                    <form
                      onSubmit={async (e) => {
                        e.preventDefault();
                        if (inputValue.trim()) {
                          await handleSendMessage(inputValue);
                        }
                      }}
                      aria-label="Chat with Fresh Assistant"
                    >
                      <label htmlFor="chat-input" className="sr-only">
                        Type your question to Fresh Assistant
                      </label>
                      <div className="flex items-center gap-4">
                        <input
                          id="chat-input"
                          type="text"
                          value={inputValue}
                          onChange={(e) => setInputValue(e.target.value)}
                          onKeyDown={async (e) => {
                            if (e.key === 'Enter' && inputValue.trim()) {
                              await handleSendMessage(inputValue);
                            }
                          }}
                          placeholder="Type your question..."
                          aria-label="Type your question to Fresh Assistant"
                          required
                          minLength={2}
                          className="flex-1 size-input px-6 text-lg border-2 border-border bg-background text-foreground placeholder:text-muted-foreground/70 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl shadow-inner transition-all duration-200 hover:border-primary/40"
                        />
                        <button
                          type="submit"
                          disabled={!inputValue.trim()}
                          className="btn-primary size-input flex-shrink-0 rounded-full flex items-center justify-center disabled:opacity-50"
                          aria-label="Send message to Fresh Assistant"
                          title="Send message"
                        >
                          <Send className="w-4 h-4" strokeWidth={1.5} aria-hidden="true" />
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="bg-card border-y border-border py-20">
        <div className="container mx-auto px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-foreground leading-[1.15] tracking-tight mb-4">
              How It Works
            </h2>
            <p className="text-lg text-muted-foreground mx-auto leading-relaxed max-w-3xl font-medium">
              Our proven process helps you quit smoking naturally, with personalized support every step of the way.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1, duration: 0.6 }}
              className="text-center bg-background p-8 rounded-xl border border-border hover:shadow-lg transition-all duration-300 backdrop-blur-sm"
            >
              <div className="size-icon-large bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <Target className="w-6 h-6 text-primary-foreground" strokeWidth={1.5} />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-3">Sign Up & Define Your Journey</h3>
              <p className="text-sm text-muted-foreground leading-relaxed font-medium">
                Create your personalized quit plan based on your smoking habits, goals, and preferences.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="text-center bg-background p-8 rounded-xl border border-border hover:shadow-lg transition-all duration-300 backdrop-blur-sm"
            >
              <div className="size-icon-large bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <TrendingUp className="w-6 h-6 text-primary-foreground" strokeWidth={1.5} />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-3">Track Your Daily Progress</h3>
              <p className="text-sm text-muted-foreground leading-relaxed font-medium">
                Monitor your journey with real-time tracking of milestones, health improvements, and savings.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-center bg-background p-8 rounded-xl border border-border hover:shadow-lg transition-all duration-300 backdrop-blur-sm"
            >
              <div className="size-icon-large bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <Eye className="w-6 h-6 text-primary-foreground" strokeWidth={1.5} />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-3">Visualize Your Growth</h3>
              <p className="text-sm text-muted-foreground leading-relaxed font-medium">
                See your progress through beautiful charts and celebrate every milestone achieved.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Web Tools Showcase Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-foreground leading-[1.15] tracking-tight mb-4">
              Comprehensive Wellness Tools
            </h2>
            <p className="text-lg text-muted-foreground mx-auto leading-relaxed max-w-3xl font-medium">
              Access our complete suite of evidence-based tools and resources designed to support every aspect of your quit smoking journey and long-term wellness.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1, duration: 0.8, ease: "easeOut" }}
              className="card"
            >
              <div className="icon-container mb-6">
                <Shield className="w-6 h-6 text-primary-foreground" strokeWidth={1.5} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">NRT Guide</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Expert guidance and personalized recommendations for nicotine replacement therapy, tailored to your needs.
              </p>
              <Link
                to="/tools/nrt-guide"
                className="text-primary hover:text-primary/80 font-medium min-h-[44px] py-2 flex items-center"
                aria-label="Learn more about NRT Guide - Expert guidance for nicotine replacement therapy"
              >
                Learn More →
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="card"
            >
              <div className="icon-container mb-6">
                <Target className="w-6 h-6 text-primary-foreground" strokeWidth={1.5} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Smokeless Directory</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Comprehensive, curated directory of smokeless alternatives with expert reviews and recommendations.
              </p>
              <Link
                to="/tools/smokeless-directory"
                className="text-primary hover:text-primary/80 font-medium min-h-[44px] py-2 flex items-center"
                aria-label="Learn more about Smokeless Directory"
              >
                Explore Directory →
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="card"
            >
              <div className="icon-container mb-6">
                <Brain className="w-6 h-6 text-primary-foreground" strokeWidth={1.5} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Quitting Methods</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Evidence-based strategies and step-by-step guides for successful smoking cessation and long-term wellness.
              </p>
              <Link to="/tools/quit-methods" className="text-primary hover:text-primary/80 font-medium">
                Find Your Method →
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="card"
            >
              <div className="icon-container mb-6">
                <TrendingUp className="w-6 h-6 text-primary-foreground" strokeWidth={1.5} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Wellness Calculator</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Track your progress, calculate health improvements, and visualize your journey to better wellness.
              </p>
              <Link
                to="/tools/calculators"
                className="text-primary hover:text-primary/80 font-medium min-h-[44px] py-2 flex items-center"
                aria-label="Learn more about Wellness Calculator"
              >
                Calculate Progress →
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="card"
            >
              <div className="icon-container mb-6">
                <Users className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Holistic Wellness</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Comprehensive guides and tools for improving sleep quality, energy levels, and mental focus.
              </p>
              <Link
                to="/tools/holistic-health"
                className="text-primary hover:text-primary/80 font-medium min-h-[44px] py-2 flex items-center"
                aria-label="Learn more about Holistic Wellness"
              >
                Explore Wellness →
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="card"
            >
              <div className="icon-container mb-6">
                <ArrowRight className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Explore All Tools</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Access our complete suite of wellness tools, resources, and expert guidance for your journey.
              </p>
              <Link
                to="/tools"
                className="text-primary hover:text-primary/80 font-medium min-h-[44px] py-2 flex items-center"
                aria-label="View all tools"
              >
                View All Tools →
              </Link>
            </motion.div>
          </div>
        </div>
      </section>



      {/* Features Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-semibold text-foreground mb-4">
              Why Choose Mission Fresh?
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our comprehensive approach combines cutting-edge technology with proven
              methods to give you the best chance of success.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="icon-container-round mx-auto mb-4">
                <Brain className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">AI-Powered Support</h3>
              <p className="text-muted-foreground">
                Get personalized guidance from our intelligent assistant that learns
                your patterns and provides tailored advice.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="icon-container-round mx-auto mb-4">
                <Target className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Personalized Plans</h3>
              <p className="text-muted-foreground">
                Customized quit plans based on your smoking history, preferences,
                and goals for maximum effectiveness.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="icon-container-round mx-auto mb-4">
                <Users className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Community Support</h3>
              <p className="text-muted-foreground">
                Connect with others on the same journey. Share experiences, get
                encouragement, and celebrate victories together.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof Section - HOLY RULE 0001: Dynamic testimonials from database */}
      <section className="py-20 bg-muted">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-6">Trusted by Thousands</h2>
            <p className="text-xl text-muted-foreground">Real success stories from our community</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {isLoadingTestimonials ? (
              // Loading state for testimonials
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="bg-background p-8 rounded-xl border border-border shadow-lg">
                  <div className="flex items-center mb-4">
                    <div className="flex text-warning">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 fill-current" />
                      ))}
                    </div>
                  </div>
                  <div className="h-20 bg-muted animate-pulse rounded mb-4"></div>
                  <div className="h-4 bg-muted animate-pulse rounded mb-2"></div>
                  <div className="h-3 bg-muted animate-pulse rounded w-2/3"></div>
                </div>
              ))
            ) : testimonials.length > 0 ? (
              // Dynamic testimonials from database
              testimonials.map((testimonial) => (
                <div key={testimonial.id} className="bg-background p-8 rounded-xl border border-border shadow-lg">
                  <div className="flex items-center mb-4">
                    <div className="flex text-warning">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 fill-current" />
                      ))}
                    </div>
                    <span className="ml-2 text-sm text-muted-foreground">({testimonial.likes_count} likes)</span>
                  </div>
                  <p className="text-muted-foreground mb-4 italic">
                    "{testimonial.content}"
                  </p>
                  <div className="font-semibold text-foreground">{testimonial.title}</div>
                  <div className="text-sm text-muted-foreground">
                    {new Date(testimonial.created_at).toLocaleDateString(undefined, {
                      month: 'long',
                      year: 'numeric'
                    })}
                  </div>
                </div>
              ))
            ) : (
              // Fallback when no testimonials available
              <div className="col-span-3 text-center py-12">
                <p className="text-muted-foreground">Join our community and share your success story!</p>
                <Link
                  to="/community"
                  className="btn-primary mt-4 inline-flex items-center gap-2"
                >
                  <Users className="w-4 h-4" />
                  Visit Community
                </Link>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Trust Indicators Section */}
      <section className="py-16 bg-muted">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Trusted & Secure</h2>
            <p className="text-lg text-muted-foreground">Your privacy and security are our top priorities</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-xl flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-primary-foreground" />
              </div>
              <h3 className="font-semibold text-foreground mb-2">HIPAA Compliant</h3>
              <p className="text-sm text-muted-foreground">Healthcare data protection</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-xl flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-primary-foreground" />
              </div>
              <h3 className="font-semibold text-foreground mb-2">SSL Encrypted</h3>
              <p className="text-sm text-muted-foreground">256-bit encryption</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-xl flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-primary-foreground" />
              </div>
              <h3 className="font-semibold text-foreground mb-2">FDA Approved</h3>
              <p className="text-sm text-muted-foreground">Evidence-based methods</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-xl flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-primary-foreground" />
              </div>
              <h3 className="font-semibold text-foreground mb-2">Expert Reviewed</h3>
              <p className="text-sm text-muted-foreground">Medical professionals</p>
            </div>
          </div>
        </div>
      </section>

      {/* User Feedback Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl font-bold text-foreground mb-6">Help Us Improve</h2>
            <p className="text-xl text-muted-foreground mb-12">Your feedback helps us create better tools and resources for everyone</p>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-card p-8 rounded-xl border border-border shadow-lg">
                <h3 className="text-2xl font-semibold text-foreground mb-4">Quick Feedback</h3>
                <p className="text-muted-foreground mb-6">How helpful is our homepage?</p>
                <div className="flex justify-center gap-2 mb-6">
                  {/* HOLY RULE #1: Rating system must be loaded from database - removed hardcoded ratings */}
                  <p className="text-muted-foreground text-sm">Rating system loading from database...</p>
                </div>
                <Link
                  to="/feedback"
                  className="btn-primary px-6 py-3 rounded-lg font-semibold transition-all duration-200"
                >
                  Leave Detailed Feedback
                </Link>
              </div>

              <div className="bg-card p-8 rounded-xl border border-border shadow-lg">
                <h3 className="text-2xl font-semibold text-foreground mb-4">Feature Requests</h3>
                <p className="text-muted-foreground mb-6">What features would help you most?</p>
                <div className="space-y-3 mb-6">
                  {/* HOLY RULE #1: Feature requests must be loaded from database - removed hardcoded options */}
                  <p className="text-muted-foreground text-sm">Feature options loading from database...</p>
                </div>
                <Link
                  to="/feature-requests"
                  className="btn-secondary px-6 py-3 rounded-lg font-semibold transition-all duration-200"
                >
                  Submit Request
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl font-semibold mb-4">
            Ready to Start Your Smoke-Free Journey?
          </h2>
          <p className="text-xl text-primary-foreground/80 mb-8 max-w-2xl mx-auto">
            Start your personalized quit smoking journey with Mission Fresh
          </p>
          <Link
            to="/auth?action=signup"
            className="inline-flex items-center gap-2 bg-secondary text-secondary-foreground px-8 py-4 min-h-[44px] rounded-lg font-semibold hover:bg-secondary-hover transition-all duration-200 border border-border hover:shadow-sm"
            aria-label="Get started with Mission Fresh"
          >
            <Sparkles className="w-5 h-5" strokeWidth={2} aria-hidden="true" />
            Get Started Now
            <ArrowRight className="w-5 h-5" strokeWidth={2} aria-hidden="true" />
          </Link>
        </div>
      </section>
    </div>
  )
}
