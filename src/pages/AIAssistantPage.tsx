import React, { useState, useEffect, useRef } from 'react';
import { Send, MessageCircle, <PERSON>rk<PERSON>, User, Bot } from 'lucide-react';
import { supabase } from '../lib/supabase';

interface AIService {
  id: string;
  name: string;
  icon: string;
  description: string;
}

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

const AIAssistantPage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [aiServices, setAIServices] = useState<AIService[]>([]);
  const [selectedService, setSelectedService] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchAIServices();
    addWelcomeMessage();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchAIServices = async () => {
    try {
      // HOLY RULE 0001: Load AI assistant features from mission_fresh.learning_modules
      const { data, error } = await supabase
        .from('mission_fresh.learning_modules')
        .select('id, title, description')
        .eq('category', 'ai_features')
        .eq('is_published', true)
        .order('order_index', { ascending: true });

      if (error) {
        console.error('Error fetching AI services:', error);
        // No fallback data per Holy Rule #1 - use empty array only
        setAIServices([]);
        setSelectedService('');
      } else if (data && data.length > 0) {
        // Map database data to AIService interface
        const dbServices: AIService[] = data.map((item, index) => ({
          id: item.id,
          name: item.title,
          icon: ['Bot', 'MessageCircle', 'Sparkles'][index] || 'Bot',
          description: item.description
        }));
        setAIServices(dbServices);
        setSelectedService(dbServices[0].id);
      } else {
        // No data found - use empty array per Holy Rule #1
        setAIServices([]);
        setSelectedService('');
      }
    } catch (error) {
      console.error('Error fetching AI services:', error);
      setAIServices([]);
    }
  };

  const addWelcomeMessage = () => {
    const welcomeMessage: Message = {
      id: 'welcome',
      content: "Hello! I'm your AI-powered quit smoking assistant. I'm here to provide personalized support, answer your questions, and help you through every step of your smoke-free journey. How can I help you today?",
      sender: 'ai',
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: "I understand you're working on your quit smoking journey. While I'm here to provide support and guidance, please remember that for specific medical advice, it's always best to consult with your healthcare provider. That said, I'm here to help with motivation, strategies, and general wellness support. What specific aspect of quitting would you like to discuss?",
        sender: 'ai',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 2000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const conversationStarters = [
    "How do I start my quit journey?",
    "What are the best quit methods?",
    "How can I handle cravings?",
    "Tell me about NRT options",
    "What should I expect when quitting?"
  ];

  const handleStarterClick = (starter: string) => {
    setInputMessage(starter);
  };

  return (
    <div className="min-h-screen bg-background">
      <main role="main" aria-label="Mission Fresh AI Assistant">

      {/* Breadcrumb Navigation */}
      <nav aria-label="Breadcrumb" className="bg-muted/30 border-b border-border">
        <div className="container mx-auto px-4 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a href="/" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Home
              </a>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <span className="text-foreground font-medium" aria-current="page">
                AI Assistant
              </span>
            </li>
          </ol>
        </div>
      </nav>

      {/* Skip navigation links - hidden by default, only visible on keyboard focus */}
      <a
        href="#chat-interface"
        className="sr-only focus:not-sr-only focus:absolute focus:top-20 focus:left-4 bg-primary text-primary-foreground px-4 py-3 min-h-[44px] min-w-[44px] flex items-center justify-center rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-primary-foreground"
      >
        Skip to chat interface
      </a>

      {/* ARIA live region for chat updates */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {isTyping ? 'AI is typing...' : `${messages.length} messages in conversation.`}
      </div>

      {/* Header */}
      <section id="ai-services" className="bg-card border-b border-border">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex items-center space-x-3 mb-2">
            <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center min-h-[44px] min-w-[44px]">
              <MessageCircle className="w-6 h-6 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-foreground">AI Assistant</h1>
              <p className="text-muted-foreground">Your personalized quit smoking support companion</p>
            </div>
          </div>

          {/* AI Service Selector */}
          {aiServices.length > 0 && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-foreground mb-2" htmlFor="ai-service-select">
                AI Service
              </label>
              <select
                value={selectedService}
                onChange={(e) => setSelectedService(e.target.value)}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                {aiServices.map((service) => (
                  <option key={service.id} value={service.id}>
                    {service.icon} {service.name} - {service.description}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </section>

      {/* Chat Container */}
      <section id="chat-interface" className="max-w-4xl mx-auto px-4 py-6">
        <div className="bg-card rounded-xl shadow-sm border border-border h-96 flex flex-col" role="log" aria-label="Chat conversation" aria-live="polite">
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4" aria-label="Chat messages">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex items-start space-x-3 ${
                  message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                }`}
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  message.sender === 'user' 
                    ? 'bg-gray-200' 
                    : 'bg-primary'
                }`}>
                  {message.sender === 'user' ? (
                    <User className="w-4 h-4 text-gray-600" />
                  ) : (
                    <Bot className="w-4 h-4 text-white" />
                  )}
                </div>
                <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.sender === 'user'
                    ? 'bg-primary text-white ml-auto'
                    : 'bg-gray-100 text-gray-900'
                }`}>
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs mt-1 opacity-70">
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
            {isTyping && (
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <div className="bg-gray-100 px-4 py-2 rounded-lg">
                  <p className="text-sm text-gray-600">AI is typing...</p>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="border-t border-border p-4">
            <form onSubmit={(e) => { e.preventDefault(); handleSendMessage(); }} className="flex space-x-2">
              <label htmlFor="chat-input" className="sr-only">
                Type your message about quitting smoking
              </label>
              <input
                id="chat-input"
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
                placeholder="Ask me anything about quitting smoking..."
                className="flex-1 px-4 py-3 min-h-[44px] border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-foreground"
                aria-describedby="chat-input-help"
                required
              />
              <div id="chat-input-help" className="sr-only">
                Type your question and press Enter or click Send to chat with the AI assistant
              </div>
              <button
                type="submit"
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isTyping}
                className="px-4 py-3 min-h-[44px] min-w-[44px] bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-primary-foreground"
                aria-label="Send message"
              >
                <Send className="w-4 h-4" />
              </button>
            </form>
          </div>
        </div>

        {/* Conversation Starters */}
        {messages.length <= 1 && (
          <div id="conversation-starters" className="mt-6">
            <h3 className="text-lg font-medium text-foreground mb-4 flex items-center">
              <Sparkles className="w-5 h-5 text-primary mr-2" />
              Get Started With These Questions
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {conversationStarters.map((starter, index) => (
                <button
                  key={index}
                  onClick={() => handleStarterClick(starter)}
                  className="text-left p-4 min-h-[44px] bg-card border border-border rounded-lg hover:border-primary hover:bg-primary/5 transition-colors focus:outline-none focus:ring-2 focus:ring-primary"
                  aria-label={`Ask: ${starter}`}
                >
                  <p className="text-sm text-foreground">{starter}</p>
                </button>
              ))}
            </div>
          </div>
        )}
      </section>
      </main>
    </div>
  );
};

export default AIAssistantPage;
