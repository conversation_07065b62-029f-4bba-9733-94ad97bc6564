import React, { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import {
  Heart,
  Flame,
  BookOpen,
  StickyNote,
  CheckCircle,
  Loader2,
  Star
} from 'lucide-react'
import InlineLoginForm from '../components/InlineLoginForm'
import ErrorBoundary from '../components/ErrorBoundary'

// Icon mapping for dynamic icon selection
const iconMap: Record<string, any> = {
  Heart,
  Flame,
  BookOpen,
  StickyNote
}

interface LogTab {
  id: string
  label: string
  icon_name: string
  display_order: number
}

export default function LogEntryPage() {
  const { user } = useAuth()
  const navigate = useNavigate()


  const [activeTab, setActiveTab] = useState('wellness')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // Add meta tags for SEO
  React.useEffect(() => {
    document.title = 'Daily Log - Mission Fresh Lite | Track Your Wellness Journey'

    // Add meta description
    let metaDescription = document.querySelector('meta[name="description"]')
    if (!metaDescription) {
      metaDescription = document.createElement('meta')
      metaDescription.setAttribute('name', 'description')
      document.head.appendChild(metaDescription)
    }
    metaDescription.setAttribute('content', 'Log your daily wellness metrics, mood, energy, cravings, and journal entries to track your smoking cessation progress.')

    // Add canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]')
    if (!canonicalLink) {
      canonicalLink = document.createElement('link')
      canonicalLink.setAttribute('rel', 'canonical')
      document.head.appendChild(canonicalLink)
    }
    canonicalLink.setAttribute('href', `${window.location.origin}/dashboard/log`)
  }, [])
  
  // RULE 0001: Dynamic tab configuration from database - hardcoded array removed
  const [tabs, setTabs] = useState<LogTab[]>([])
  const [tabsLoading, setTabsLoading] = useState(true)
  
  // Wellness states
  const [mood, setMood] = useState(3)
  const [energy, setEnergy] = useState(3)
  const [stress, setStress] = useState(3)
  const [sleepHours, setSleepHours] = useState('')
  const [sleepQuality, setSleepQuality] = useState(3)
  
  // Craving states
  const [cravingIntensity, setCravingIntensity] = useState(0)
  const [cravingTrigger, setCravingTrigger] = useState('')
  const [copingStrategy, setCopingStrategy] = useState('')
  
  // Journal and notes
  const [journalEntry, setJournalEntry] = useState('')
  const [dailyNotes, setDailyNotes] = useState('')
  
  useEffect(() => {
    fetchLogTabs()
  }, [])

  const fetchLogTabs = async () => {
    try {
      // RULE 0001: Load tabs from database - no hardcoded data
      const { data: tabsData, error } = await supabase
        .from('log_tabs')
        .select('*')
        .order('display_order')

      if (error) {
        // No fallback data per Holy Rule #1 - use empty array only
        setTabs([])
      } else {
        setTabs(tabsData || [])
      }
    } catch (err) {
      // HOLY RULE #1: No hardcoded dynamic data - use empty array only
      setTabs([])
    } finally {
      setTabsLoading(false)
    }
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setLoading(true)
    setError('')

    try {
      // Save daily health summary
      const { error: healthError } = await supabase
        .from('daily_health_summaries')
        .insert([{
          user_id: user.id,
          date: new Date().toISOString().split('T')[0],
          mood_score: mood,
          energy_level: energy,
          stress_level: stress,
          sleep_hours: sleepHours ? parseFloat(sleepHours) : null,
          sleep_quality: sleepQuality,
          notes: dailyNotes
        }])
      
      if (healthError) throw healthError
      
      // Save craving log if there was a craving
      if (cravingIntensity > 0) {
        const { error: cravingError } = await supabase
          .from('craving_logs')
          .insert([{
            user_id: user.id,
            intensity: cravingIntensity,
            trigger: cravingTrigger,
            coping_strategy: copingStrategy,
            logged_at: new Date().toISOString()
          }])
        
        if (cravingError) throw cravingError
      }
      
      // Save journal entry if provided
      if (journalEntry.trim()) {
        const { error: journalError } = await supabase
          .from('journal_entries')
          .insert([{
            user_id: user.id,
            content: journalEntry,
            mood: mood,
            created_at: new Date().toISOString()
          }])
        
        if (journalError) throw journalError
      }
      
      // Success - navigate back to dashboard
      navigate('/dashboard')
      
    } catch (error) {
      console.error('Error saving log entry:', error)
      setError('Failed to save your daily log. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  // HOLY RULE #1: Enforce authentication - show inline login form for better UX
  if (!user) {
    return (
      <InlineLoginForm
        title="Access Your Daily Log"
        subtitle="Sign in to track your wellness journey and log your progress"
      />
    )
  }

  // Add loading state for tabs
  if (tabsLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-16 h-16 animate-spin mx-auto mb-6 text-primary" strokeWidth={1.5} />
          <p className="text-muted-foreground text-lg">Loading daily log...</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-background">
      {/* Breadcrumb Navigation */}
      <nav aria-label="Breadcrumb" className="bg-background/80 backdrop-blur-sm border-b border-border">
        <div className="container mx-auto px-4 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a href="/" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Home
              </a>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <a href="/dashboard" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Dashboard
              </a>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <span className="text-foreground font-medium" aria-current="page">
                Daily Log
              </span>
            </li>
          </ol>
        </div>
      </nav>

      {/* Skip navigation links - hidden by default, only visible on keyboard focus */}
      <a
        href="#log-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-20 focus:left-4 bg-primary text-primary-foreground px-4 py-3 min-h-[44px] min-w-[44px] flex items-center justify-center rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-primary-foreground"
      >
        Skip to log content
      </a>

      {/* ARIA live region for dynamic updates */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {error && `Error: ${error}`}
        {loading && 'Saving daily log...'}
      </div>

      <main role="main" aria-label="Daily Log Entry" className="py-12">
        <div className="max-w-5xl mx-auto px-6 lg:px-8">
          {/* Error Display */}
          {error && (
            <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-6 mb-8" role="alert" aria-live="polite">
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-destructive rounded-lg flex items-center justify-center">
                  <Heart className="w-5 h-5 text-destructive-foreground" strokeWidth={2} />
                </div>
                <div>
                  <h3 className="font-bold text-destructive mb-1">Error</h3>
                  <p className="text-destructive/80">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Header */}
          <header className="text-center mb-16" role="banner" id="log-content">
            <h1 className="text-5xl font-bold text-foreground mb-6 tracking-tight" id="log-main-heading">
              Daily Log
            </h1>
            <p className="text-xl text-muted-foreground leading-relaxed" aria-describedby="log-main-heading">
              Track your wellness journey and celebrate your progress
            </p>
          </header>

          <ErrorBoundary>
            <form onSubmit={handleSubmit} className="bg-card rounded-xl shadow-lg border border-border" role="form" aria-labelledby="log-form-heading">
              <h2 id="log-form-heading" className="sr-only">Daily Log Entry Form</h2>

            {/* Tab Navigation */}
            <div className="border-b border-border" role="tablist" aria-label="Daily log categories">
              <nav className="flex space-x-12 px-8">
                {tabs.map((tab) => {
                  const IconComponent = iconMap[tab.icon_name] || Heart
                  return (
                    <button
                      key={tab.id}
                      type="button"
                      role="tab"
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-6 px-2 relative font-semibold text-base transition-all duration-300 min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                        activeTab === tab.id
                          ? 'text-primary border-b-2 border-primary'
                          : 'text-muted-foreground hover:text-foreground'
                      }`}
                      aria-selected={activeTab === tab.id}
                      aria-controls={`${tab.id}-panel`}
                      id={`${tab.id}-tab`}
                    >
                      <div className="flex items-center space-x-3">
                        <IconComponent className="w-5 h-5" strokeWidth={2} aria-hidden="true" />
                        <span>{tab.label}</span>
                      </div>
                    </button>
                  )
                })}
              </nav>
            </div>
          
            {/* Tab Content */}
            <div className="p-8 space-y-10">
              {activeTab === 'wellness' && (
                <div
                  className="space-y-10"
                  role="tabpanel"
                  id="wellness-panel"
                  aria-labelledby="wellness-tab"
                  tabIndex={0}
                >
                  <h3 className="text-2xl font-bold text-card-foreground">How are you feeling today?</h3>

                  {/* Mood */}
                  <fieldset>
                    <legend className="block text-base font-semibold text-card-foreground mb-6">Mood Rating</legend>
                    <div className="flex space-x-6" role="radiogroup" aria-labelledby="mood-legend">
                      {[1, 2, 3, 4, 5].map((rating) => (
                        <button
                          key={rating}
                          type="button"
                          onClick={() => setMood(rating)}
                          className={`w-16 h-16 rounded-xl border-2 flex items-center justify-center transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                            mood === rating
                              ? 'border-primary bg-primary/10 text-primary shadow-lg'
                              : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'
                          }`}
                          role="radio"
                          aria-checked={mood === rating}
                          aria-label={`Mood rating ${rating} out of 5`}
                        >
                          <Star className={`w-5 h-5 ${mood === rating ? 'fill-current' : ''}`} strokeWidth={2} aria-hidden="true" />
                        </button>
                      ))}
                    </div>
                  </fieldset>
                
                  {/* Energy */}
                  <fieldset>
                    <legend className="block text-base font-semibold text-card-foreground mb-6">Energy Level Rating</legend>
                    <div className="flex space-x-6" role="radiogroup" aria-labelledby="energy-legend">
                      {[1, 2, 3, 4, 5].map((rating) => (
                        <button
                          key={rating}
                          type="button"
                          onClick={() => setEnergy(rating)}
                          className={`w-16 h-16 rounded-xl border-2 flex items-center justify-center transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                            energy === rating
                              ? 'border-primary bg-primary/10 text-primary shadow-lg'
                              : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'
                          }`}
                          role="radio"
                          aria-checked={energy === rating}
                          aria-label={`Energy level rating ${rating} out of 5`}
                        >
                          <Star className={`w-5 h-5 ${energy === rating ? 'fill-current' : ''}`} strokeWidth={2} aria-hidden="true" />
                        </button>
                      ))}
                    </div>
                  </fieldset>

                  {/* Stress */}
                  <fieldset>
                    <legend className="block text-base font-semibold text-card-foreground mb-6">Stress Level Rating</legend>
                    <div className="flex space-x-6" role="radiogroup" aria-labelledby="stress-legend">
                      {[1, 2, 3, 4, 5].map((rating) => (
                        <button
                          key={rating}
                          type="button"
                          onClick={() => setStress(rating)}
                          className={`w-16 h-16 rounded-xl border-2 flex items-center justify-center transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                            stress === rating
                              ? 'border-primary bg-primary/10 text-primary shadow-lg'
                              : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'
                          }`}
                          role="radio"
                          aria-checked={stress === rating}
                          aria-label={`Stress level rating ${rating} out of 5`}
                        >
                          <Star className={`w-5 h-5 ${stress === rating ? 'fill-current' : ''}`} strokeWidth={2} aria-hidden="true" />
                        </button>
                      ))}
                    </div>
                  </fieldset>
                
                  {/* Sleep */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
                    <div>
                      <label htmlFor="sleepHours" className="block text-base font-semibold text-card-foreground mb-4">
                        Hours of Sleep
                      </label>
                      <input
                        type="number"
                        id="sleepHours"
                        min="0"
                        max="24"
                        step="0.5"
                        value={sleepHours}
                        onChange={(e) => setSleepHours(e.target.value)}
                        className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg min-h-[44px]"
                        placeholder="8"
                        aria-describedby="sleep-hours-help"
                      />
                      <p id="sleep-hours-help" className="text-sm text-muted-foreground mt-2">
                        Enter the number of hours you slept (e.g., 7.5)
                      </p>
                    </div>

                    <fieldset>
                      <legend className="block text-base font-semibold text-card-foreground mb-6">Sleep Quality Rating</legend>
                      <div className="flex space-x-4" role="radiogroup" aria-labelledby="sleep-quality-legend">
                        {[1, 2, 3, 4, 5].map((rating) => (
                          <button
                            key={rating}
                            type="button"
                            onClick={() => setSleepQuality(rating)}
                            className={`w-14 h-14 rounded-xl border-2 flex items-center justify-center transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                              sleepQuality === rating
                                ? 'border-primary bg-primary/10 text-primary shadow-lg'
                                : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'
                            }`}
                            role="radio"
                            aria-checked={sleepQuality === rating}
                            aria-label={`Sleep quality rating ${rating} out of 5`}
                          >
                            <Star className={`w-5 h-5 ${sleepQuality === rating ? 'fill-current' : ''}`} strokeWidth={2} aria-hidden="true" />
                          </button>
                        ))}
                      </div>
                    </fieldset>
                  </div>
              </div>
            )}
            
              {activeTab === 'cravings' && (
                <div
                  className="space-y-10"
                  role="tabpanel"
                  id="cravings-panel"
                  aria-labelledby="cravings-tab"
                  tabIndex={0}
                >
                  <h3 className="text-2xl font-bold text-card-foreground">Craving Management</h3>

                  {/* Craving Intensity */}
                  <fieldset>
                    <legend className="block text-base font-semibold text-card-foreground mb-6">
                      Craving Intensity (0 = No craving, 10 = Intense craving)
                    </legend>
                    <div className="flex space-x-3" role="radiogroup" aria-labelledby="craving-intensity-legend">
                      {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((intensity) => (
                        <button
                          key={intensity}
                          type="button"
                          onClick={() => setCravingIntensity(intensity)}
                          className={`w-12 h-12 rounded-lg border-2 flex items-center justify-center text-sm font-bold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                            cravingIntensity === intensity
                              ? 'border-primary bg-primary/10 text-primary shadow-lg'
                              : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'
                          }`}
                          role="radio"
                          aria-checked={cravingIntensity === intensity}
                          aria-label={`Craving intensity ${intensity} out of 10`}
                        >
                          {intensity}
                        </button>
                      ))}
                    </div>
                  </fieldset>
                
                  {cravingIntensity > 0 && (
                    <>
                      {/* Trigger */}
                      <div>
                        <label htmlFor="trigger" className="block text-base font-semibold text-card-foreground mb-4">
                          What triggered this craving?
                        </label>
                        <input
                          type="text"
                          id="trigger"
                          value={cravingTrigger}
                          onChange={(e) => setCravingTrigger(e.target.value)}
                          className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg min-h-[44px]"
                          placeholder="E.g., stress, social situation, habit..."
                          aria-describedby="trigger-help"
                        />
                        <p id="trigger-help" className="text-sm text-muted-foreground mt-2">
                          Identifying triggers helps you prepare for future situations
                        </p>
                      </div>

                      {/* Coping Strategy */}
                      <div>
                        <label htmlFor="coping" className="block text-base font-semibold text-card-foreground mb-4">
                          How did you cope with it?
                        </label>
                        <input
                          type="text"
                          id="coping"
                          value={copingStrategy}
                          onChange={(e) => setCopingStrategy(e.target.value)}
                          className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg min-h-[44px]"
                          placeholder="E.g., deep breathing, exercise, distraction..."
                          aria-describedby="coping-help"
                        />
                        <p id="coping-help" className="text-sm text-muted-foreground mt-2">
                          Recording successful coping strategies builds your toolkit
                        </p>
                      </div>
                    </>
                  )}
              </div>
            )}
            
              {activeTab === 'journal' && (
                <div
                  className="space-y-10"
                  role="tabpanel"
                  id="journal-panel"
                  aria-labelledby="journal-tab"
                  tabIndex={0}
                >
                  <h3 className="text-2xl font-bold text-card-foreground">Daily Reflection</h3>
                  <div>
                    <label htmlFor="journal" className="block text-base font-semibold text-card-foreground mb-4">
                      How was your day? What are you grateful for?
                    </label>
                    <textarea
                      id="journal"
                      rows={8}
                      value={journalEntry}
                      onChange={(e) => setJournalEntry(e.target.value)}
                      className="w-full px-4 py-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none"
                      placeholder="Write your thoughts, feelings, and reflections..."
                      aria-describedby="journal-help"
                    />
                    <p id="journal-help" className="text-sm text-muted-foreground mt-2">
                      Journaling helps process emotions and track your mental wellness journey
                    </p>
                  </div>
                </div>
              )}

              {activeTab === 'notes' && (
                <div
                  className="space-y-10"
                  role="tabpanel"
                  id="notes-panel"
                  aria-labelledby="notes-tab"
                  tabIndex={0}
                >
                  <h3 className="text-2xl font-bold text-card-foreground">Additional Notes</h3>
                  <div>
                    <label htmlFor="notes" className="block text-base font-semibold text-card-foreground mb-4">
                      Any other notes for today?
                    </label>
                    <textarea
                      id="notes"
                      rows={6}
                      value={dailyNotes}
                      onChange={(e) => setDailyNotes(e.target.value)}
                      className="w-full px-4 py-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none"
                      placeholder="Additional thoughts, reminders, or observations..."
                      aria-describedby="notes-help"
                    />
                    <p id="notes-help" className="text-sm text-muted-foreground mt-2">
                      Use this space for any additional observations or reminders
                    </p>
                  </div>
                </div>
              )}
          </div>
          
            {/* Submit Button */}
            <div className="px-8 py-8 bg-muted/30 border-t border-border rounded-b-xl">
              <button
                type="submit"
                disabled={loading}
                className="w-full flex items-center justify-center px-6 py-4 border border-transparent text-lg font-semibold rounded-lg text-primary-foreground bg-primary hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl min-h-[44px]"
                aria-describedby="submit-help"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-3 animate-spin" strokeWidth={2} aria-hidden="true" />
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-5 h-5 mr-3" strokeWidth={2} aria-hidden="true" />
                    <span>Save Daily Log</span>
                  </>
                )}
              </button>
              <p id="submit-help" className="text-sm text-muted-foreground mt-3 text-center">
                Your daily log will be saved securely and help track your wellness progress
              </p>
            </div>
            </form>
          </ErrorBoundary>
        </div>
      </main>
    </div>
  )
}
