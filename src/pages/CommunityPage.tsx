import React, { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'
import {
  MessageCircle,
  Heart,
  Users,
  Plus,
  TrendingUp,
  Clock,
  Star,
  Award,
  Send
} from 'lucide-react'

interface CommunityPost {
  id: string
  title: string
  content: string
  post_type: string
  likes_count: number
  created_at: string
  updated_at: string
  user_id: string

}

export default function CommunityPage() {
  const { user } = useAuth()
  const [posts, setPosts] = useState<CommunityPost[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreatePost, setShowCreatePost] = useState(false)
  const [selectedFilter, setSelectedFilter] = useState('all')
  const [error, setError] = useState('')

  // Add meta tags for SEO
  React.useEffect(() => {
    document.title = 'Community Support - Mission Fresh Lite | Connect with Others on Your Wellness Journey'

    // Add meta description
    let metaDescription = document.querySelector('meta[name="description"]')
    if (!metaDescription) {
      metaDescription = document.createElement('meta')
      metaDescription.setAttribute('name', 'description')
      document.head.appendChild(metaDescription)
    }
    metaDescription.setAttribute('content', 'Join our supportive community of people on their smoking cessation journey. Share success stories, get support, and connect with others.')

    // Add canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]')
    if (!canonicalLink) {
      canonicalLink = document.createElement('link')
      canonicalLink.setAttribute('rel', 'canonical')
      document.head.appendChild(canonicalLink)
    }
    canonicalLink.setAttribute('href', `${window.location.origin}/dashboard/community`)
  }, [])

  useEffect(() => {
    loadPosts()
  }, [selectedFilter])

  const loadPosts = async () => {
    try {
      setLoading(true)
      let query = supabase
        .from('community_posts')
        .select('*')
        .order('created_at', { ascending: false })

      if (selectedFilter !== 'all') {
        query = query.eq('post_type', selectedFilter)
      }

      const { data, error } = await query

      if (error) throw error
      setPosts(data || [])
    } catch (error) {
      console.error('Error loading posts:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLike = async (postId: string) => {
    if (!user) return

    try {
      const post = posts.find(p => p.id === postId)
      if (!post) return

      const { error } = await supabase
        .from('community_posts')
        .update({ likes_count: post.likes_count + 1 })
        .eq('id', postId)

      if (error) throw error

      setPosts(posts.map(p => 
        p.id === postId 
          ? { ...p, likes_count: p.likes_count + 1 }
          : p
      ))
    } catch (error) {
      console.error('Error liking post:', error)
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffDays > 0) return `${diffDays}d ago`
    if (diffHours > 0) return `${diffHours}h ago`
    return 'Just now'
  }

  // RULE 0001: Load post types dynamically from database only - no hardcoded fallbacks
  const [postTypes, setPostTypes] = useState<Array<{
    value: string
    label: string
    icon: any
  }>>([])

  // Load post types from database (fallback to static if table doesn't exist)
  useEffect(() => {
    const loadPostTypes = async () => {
      try {
        const { data, error } = await supabase
          .from('post_types')
          .select('*')
          .order('display_order')
        
        if (data && data.length > 0) {
          // Convert database format to component format
          const dynamicPostTypes = [
            { value: 'all', label: 'All Posts', icon: Users },
            ...data.map(type => ({
              value: type.value,
              label: type.label,
              icon: type.icon_name === 'Star' ? Star : 
                    type.icon_name === 'Heart' ? Heart :
                    type.icon_name === 'Award' ? Award :
                    type.icon_name === 'MessageCircle' ? MessageCircle : Users
            }))
          ]
          setPostTypes(dynamicPostTypes)
        }
      } catch (error) {
        // Keep fallback postTypes if database query fails
        console.log('Using fallback post types (post_types table not found)')
      }
    }
    loadPostTypes()
  }, [])

  return (
    <div className="min-h-screen bg-background">
      {/* Breadcrumb Navigation */}
      <nav aria-label="Breadcrumb" className="bg-background/80 backdrop-blur-sm border-b border-border">
        <div className="container mx-auto px-4 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a href="/" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Home
              </a>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <a href="/dashboard" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Dashboard
              </a>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <span className="text-foreground font-medium" aria-current="page">
                Community
              </span>
            </li>
          </ol>
        </div>
      </nav>

      {/* Skip navigation links - hidden by default, only visible on keyboard focus */}
      <a
        href="#community-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-20 focus:left-4 bg-primary text-primary-foreground px-4 py-3 min-h-[44px] min-w-[44px] flex items-center justify-center rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-primary-foreground"
      >
        Skip to community content
      </a>

      {/* ARIA live region for dynamic updates */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {error && `Error: ${error}`}
        {loading && 'Loading community posts...'}
      </div>

      <main role="main" aria-label="Community Support" className="max-w-6xl mx-auto px-6 lg:px-8 py-12">
        {/* Error Display */}
        {error && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-6 mb-8" role="alert" aria-live="polite">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-destructive rounded-lg flex items-center justify-center">
                <MessageCircle className="w-5 h-5 text-destructive-foreground" strokeWidth={2} />
              </div>
              <div>
                <h3 className="font-bold text-destructive mb-1">Error</h3>
                <p className="text-destructive/80">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Header */}
        <header className="text-center mb-16" role="banner" id="community-content">
          <h1 className="text-5xl font-bold text-foreground mb-6 tracking-tight" id="community-main-heading">
            Community Support
          </h1>
          <p className="text-xl text-muted-foreground leading-relaxed" aria-describedby="community-main-heading">
            Connect with others on similar wellness journeys
          </p>
        </header>

        {/* Stats */}
        <section aria-labelledby="community-stats-heading" className="mb-16">
          <h2 id="community-stats-heading" className="sr-only">Community Statistics Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <article
              className="bg-card rounded-xl shadow-lg border border-border p-8 text-center hover:shadow-xl transition-all duration-300 focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
              role="article"
              aria-labelledby="posts-count-label"
              tabIndex={0}
            >
              <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20">
                <Users className="w-8 h-8 text-primary" strokeWidth={1.5} aria-hidden="true" />
              </div>
              <h3 id="posts-count-label" className="text-4xl font-bold text-card-foreground mb-2" aria-label={`${posts.length} community posts`}>
                {posts.length}
              </h3>
              <p className="text-muted-foreground text-lg">Community Posts</p>
            </article>

            <article
              className="bg-card rounded-xl shadow-lg border border-border p-8 text-center hover:shadow-xl transition-all duration-300 focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
              role="article"
              aria-labelledby="likes-count-label"
              tabIndex={0}
            >
              <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20">
                <TrendingUp className="w-8 h-8 text-primary" strokeWidth={1.5} aria-hidden="true" />
              </div>
              <h3 id="likes-count-label" className="text-4xl font-bold text-card-foreground mb-2" aria-label={`${posts.reduce((sum, post) => sum + (post.likes_count || 0), 0)} total likes`}>
                {posts.reduce((sum, post) => sum + (post.likes_count || 0), 0)}
              </h3>
              <p className="text-muted-foreground text-lg">Total Likes</p>
            </article>

            <article
              className="bg-card rounded-xl shadow-lg border border-border p-8 text-center hover:shadow-xl transition-all duration-300 focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
              role="article"
              aria-labelledby="success-stories-label"
              tabIndex={0}
            >
              <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20">
                <Star className="w-8 h-8 text-primary" strokeWidth={1.5} aria-hidden="true" />
              </div>
              <h3 id="success-stories-label" className="text-4xl font-bold text-card-foreground mb-2" aria-label={`${posts.filter(p => p.post_type === 'success').length} success stories`}>
                {posts.filter(p => p.post_type === 'success').length}
              </h3>
              <p className="text-muted-foreground text-lg">Success Stories</p>
            </article>
          </div>
        </section>

        {/* Actions and Filters */}
        <section className="bg-card rounded-xl shadow-lg border border-border p-8 mb-12" aria-labelledby="filters-heading">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
            <fieldset className="flex flex-wrap gap-3">
              <legend id="filters-heading" className="sr-only">Filter community posts by type</legend>
              <div className="flex flex-wrap gap-3" role="radiogroup" aria-labelledby="filters-heading">
                {postTypes.map((type) => (
                  <button
                    key={type.value}
                    onClick={() => setSelectedFilter(type.value)}
                    className={`inline-flex items-center px-5 py-3 rounded-lg font-semibold transition-all duration-300 min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                      selectedFilter === type.value
                        ? 'bg-primary text-primary-foreground shadow-lg'
                        : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                    }`}
                    role="radio"
                    aria-checked={selectedFilter === type.value}
                    aria-label={`Filter by ${type.label.toLowerCase()}`}
                  >
                    <type.icon className="w-5 h-5 mr-3" strokeWidth={1.5} aria-hidden="true" />
                    {type.label}
                  </button>
                ))}
              </div>
            </fieldset>

            {user && (
              <button
                onClick={() => setShowCreatePost(true)}
                className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg font-semibold hover:bg-primary-hover transition-all duration-300 shadow-lg hover:shadow-xl min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                aria-label="Open form to create a new community post"
              >
                <Plus className="w-5 h-5 mr-3" strokeWidth={1.5} aria-hidden="true" />
                Share Your Story
              </button>
            )}
          </div>
        </section>

        {/* Posts */}
        <section className="space-y-8" aria-labelledby="posts-heading">
          <h2 id="posts-heading" className="sr-only">Community Posts</h2>
          {loading ? (
            <div className="text-center py-16" role="status" aria-label="Loading community posts">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto" aria-hidden="true"></div>
              <p className="mt-4 text-muted-foreground">Loading posts...</p>
            </div>
          ) : posts.length > 0 ? (
            <div role="list" aria-label="Community posts">
              {posts.map((post) => (
                <article
                  key={post.id}
                  className="bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300 focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
                  role="listitem"
                  tabIndex={0}
                  aria-labelledby={`post-${post.id}-title`}
                >
                  <header className="flex items-start justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20">
                        <Users className="w-6 h-6 text-primary" strokeWidth={1.5} aria-hidden="true" />
                      </div>
                      <div>
                        <h3 className="font-bold text-card-foreground text-lg">
                          Community Member
                        </h3>
                        <div className="flex items-center space-x-3 text-muted-foreground mt-1">
                          <Clock className="w-4 h-4" strokeWidth={1.5} aria-hidden="true" />
                          <time dateTime={post.created_at} aria-label={`Posted ${formatTimeAgo(post.created_at)}`}>
                            {formatTimeAgo(post.created_at)}
                          </time>
                          <span className="px-3 py-1 bg-muted rounded-full text-sm font-medium" aria-label={`Post type: ${post.post_type}`}>
                            {post.post_type}
                          </span>
                        </div>
                      </div>
                    </div>
                  </header>

                  <h4 id={`post-${post.id}-title`} className="text-2xl font-bold text-card-foreground mb-4">{post.title}</h4>
                  <p className="text-muted-foreground leading-relaxed mb-6 text-lg">{post.content}</p>

                  <footer className="flex items-center justify-between pt-6 border-t border-border">
                    <button
                      onClick={() => handleLike(post.id)}
                      className="inline-flex items-center text-muted-foreground hover:text-primary transition-all duration-300 font-semibold min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                      disabled={!user}
                      aria-label={`Like this post. Currently has ${post.likes_count} ${post.likes_count === 1 ? 'like' : 'likes'}`}
                    >
                      <Heart className="w-5 h-5 mr-3" strokeWidth={1.5} aria-hidden="true" />
                      {post.likes_count} {post.likes_count === 1 ? 'Like' : 'Likes'}
                    </button>

                    <button
                      className="text-primary hover:text-primary-hover font-semibold transition-all duration-300 inline-flex items-center min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                      aria-label="Reply to this post"
                    >
                      <MessageCircle className="w-5 h-5 mr-2" strokeWidth={1.5} aria-hidden="true" />
                      Reply
                    </button>
                  </footer>
                </article>
              ))}
            </div>
          ) : (
            <div className="text-center py-20 bg-card rounded-xl shadow-lg border border-border" role="status" aria-label="No community posts available">
              <div className="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20">
                <MessageCircle className="w-10 h-10 text-primary" strokeWidth={1.5} aria-hidden="true" />
              </div>
              <h3 className="text-2xl font-bold text-card-foreground mb-4">No posts yet</h3>
              <p className="text-muted-foreground mb-8 text-lg">Be the first to share your story with the community!</p>
              {user && (
                <button
                  onClick={() => setShowCreatePost(true)}
                  className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg font-semibold hover:bg-primary-hover transition-all duration-300 shadow-lg hover:shadow-xl min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                  aria-label="Create your first community post"
                >
                  <Plus className="w-5 h-5 mr-3" strokeWidth={1.5} aria-hidden="true" />
                  Create First Post
                </button>
              )}
            </div>
          )}
        </section>
      </main>

      {/* Create Post Modal */}
      {showCreatePost && (
        <CreatePostModal
          onClose={() => setShowCreatePost(false)}
          onSuccess={() => {
            setShowCreatePost(false)
            loadPosts()
          }}
        />
      )}
    </div>
  )
}

// Create Post Modal Component
interface CreatePostModalProps {
  onClose: () => void
  onSuccess: () => void
}

function CreatePostModal({ onClose, onSuccess }: CreatePostModalProps) {
  const { user } = useAuth()
  const [title, setTitle] = useState('')
  const [content, setContent] = useState('')
  const [postType, setPostType] = useState('support')
  const [submitting, setSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !title.trim() || !content.trim()) return

    try {
      setSubmitting(true)
      const { error } = await supabase
        .from('community_posts')
        .insert({
          title: title.trim(),
          content: content.trim(),
          post_type: postType,
          user_id: user.id,
          likes_count: 0
        })

      if (error) throw error
      onSuccess()
    } catch (error) {
      console.error('Error creating post:', error)
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      role="dialog"
      aria-modal="true"
      aria-labelledby="create-post-title"
      aria-describedby="create-post-description"
    >
      <div className="bg-card rounded-xl shadow-2xl w-full max-w-2xl border border-border">
        <header className="px-8 py-6 border-b border-border">
          <h3 id="create-post-title" className="text-2xl font-bold text-card-foreground">Share Your Story</h3>
          <p id="create-post-description" className="text-sm text-muted-foreground mt-2">
            Create a new post to share with the community
          </p>
        </header>

        <form onSubmit={handleSubmit} className="p-8 space-y-6">
          <div>
            <label htmlFor="post-type" className="block text-base font-semibold text-card-foreground mb-3">
              Post Type
            </label>
            <select
              id="post-type"
              value={postType}
              onChange={(e) => setPostType(e.target.value)}
              className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
              aria-describedby="post-type-help"
            >
              <option value="support">Support Request</option>
              <option value="success">Success Story</option>
              <option value="milestone">Milestone</option>
              <option value="question">Question</option>
            </select>
            <p id="post-type-help" className="text-xs text-muted-foreground mt-1">
              Choose the category that best describes your post
            </p>
          </div>

          <div>
            <label htmlFor="post-title" className="block text-base font-semibold text-card-foreground mb-3">
              Title
            </label>
            <input
              id="post-title"
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Give your post a descriptive title..."
              className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
              required
              aria-describedby="title-help"
            />
            <p id="title-help" className="text-xs text-muted-foreground mt-1">
              Create a clear, descriptive title for your post
            </p>
          </div>

          <div>
            <label htmlFor="post-content" className="block text-base font-semibold text-card-foreground mb-3">
              Content
            </label>
            <textarea
              id="post-content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Share your thoughts, experiences, or questions..."
              rows={8}
              className="w-full px-4 py-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none"
              required
              aria-describedby="content-help"
            />
            <p id="content-help" className="text-xs text-muted-foreground mt-1">
              Share your story, ask questions, or offer support to the community
            </p>
          </div>

          <footer className="flex space-x-4 pt-6">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-6 py-3 border border-border text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold text-lg min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              aria-label="Cancel post creation and close form"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={submitting || !title.trim() || !content.trim()}
              className="flex-1 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed font-semibold text-lg shadow-lg hover:shadow-xl min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary-foreground focus:ring-offset-2"
              aria-label={submitting ? 'Creating post...' : 'Create and publish post'}
            >
              {submitting ? (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-foreground mx-auto" aria-hidden="true"></div>
              ) : (
                <>
                  <Send className="w-5 h-5 mr-3 inline" strokeWidth={1.5} aria-hidden="true" />
                  Post
                </>
              )}
            </button>
          </footer>
        </form>
      </div>
    </div>
  )
}
