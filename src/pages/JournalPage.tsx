import React, { useState, useEffect } from 'react'
import { BookOpen, Calendar as CalendarIcon, Loader2, Plus, Search } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'
import InlineLoginForm from '../components/InlineLoginForm'

interface JournalEntry {
  id: string
  content: string
  created_at: string
  updated_at: string
  user_id: string
}

export default function JournalPage() {
  const { user } = useAuth()

  const [entries, setEntries] = useState<JournalEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [newEntry, setNewEntry] = useState('')
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [searchQuery, setSearchQuery] = useState('')
  const [error, setError] = useState('')

  // Add meta tags for SEO
  React.useEffect(() => {
    document.title = 'Journal - Mission Fresh Lite | Personal Wellness Journal'

    // Add meta description
    let metaDescription = document.querySelector('meta[name="description"]')
    if (!metaDescription) {
      metaDescription = document.createElement('meta')
      metaDescription.setAttribute('name', 'description')
      document.head.appendChild(metaDescription)
    }
    metaDescription.setAttribute('content', 'Express your thoughts and track your emotional journey with our personal wellness journal. Write, reflect, and monitor your quit smoking progress.')

    // Add canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]')
    if (!canonicalLink) {
      canonicalLink = document.createElement('link')
      canonicalLink.setAttribute('rel', 'canonical')
      document.head.appendChild(canonicalLink)
    }
    canonicalLink.setAttribute('href', `${window.location.origin}/dashboard/journal`)
  }, [])

  // Load journal entries
  useEffect(() => {
    if (user) {
      loadEntries()
    }
  }, [user, selectedDate])

  const loadEntries = async () => {
    if (!user) return

    setLoading(true)
    setError('')
    try {
      const startOfDay = new Date(selectedDate)
      startOfDay.setHours(0, 0, 0, 0)

      const endOfDay = new Date(selectedDate)
      endOfDay.setHours(23, 59, 59, 999)

      const { data, error } = await supabase
        .from('journal_entries')
        .select('*')
        .eq('user_id', user.id)
        .gte('created_at', startOfDay.toISOString())
        .lte('created_at', endOfDay.toISOString())
        .order('created_at', { ascending: false })

      if (error) throw error
      setEntries(data || [])
    } catch (error: any) {
      console.error('Error loading entries:', error)
      setError('Failed to load journal entries. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const saveEntry = async () => {
    if (!user || !newEntry.trim()) return

    setSaving(true)
    setError('')
    try {
      const { error } = await supabase
        .from('journal_entries')
        .insert([
          {
            content: newEntry.trim(),
            user_id: user.id
          }
        ])
        .select()

      if (error) throw error

      setNewEntry('')
      loadEntries() // Reload entries to show the new one
    } catch (error: any) {
      console.error('Error saving entry:', error)
      setError('Failed to save journal entry. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatDisplayDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const filteredEntries = entries.filter(entry =>
    entry.content.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // HOLY RULE #1: Enforce authentication - show inline login form for better UX
  if (!user) {
    return (
      <InlineLoginForm
        title="Access Your Journal"
        subtitle="Sign in to view your personal thoughts and reflections"
      />
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Breadcrumb Navigation */}
      <nav aria-label="Breadcrumb" className="bg-background/80 backdrop-blur-sm border-b border-border">
        <div className="container mx-auto px-4 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a href="/" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Home
              </a>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <a href="/dashboard" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Dashboard
              </a>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <span className="text-foreground font-medium" aria-current="page">
                Journal
              </span>
            </li>
          </ol>
        </div>
      </nav>

      {/* Skip navigation links - hidden by default, only visible on keyboard focus */}
      <a
        href="#journal-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-20 focus:left-4 bg-primary text-primary-foreground px-4 py-3 min-h-[44px] min-w-[44px] flex items-center justify-center rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-primary-foreground"
      >
        Skip to journal content
      </a>

      {/* ARIA live region for dynamic updates */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {error && `Error: ${error}`}
        {loading && 'Loading journal entries...'}
        {saving && 'Saving journal entry...'}
      </div>

      <main role="main" aria-label="Journal" className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        {/* Error Display */}
        {error && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-6 mb-8" role="alert" aria-live="polite">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-destructive rounded-lg flex items-center justify-center">
                <BookOpen className="w-5 h-5 text-destructive-foreground" strokeWidth={2} />
              </div>
              <div>
                <h3 className="font-bold text-destructive mb-1">Error</h3>
                <p className="text-destructive/80">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Header */}
        <header className="mb-16" role="banner" id="journal-content">
          <div className="flex items-center gap-6 mb-6">
            <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20">
              <BookOpen className="w-8 h-8 text-primary" strokeWidth={1.5} aria-hidden="true" />
            </div>
            <div>
              <h1 id="journal-main-heading" className="text-5xl font-bold text-foreground tracking-tight">Journal</h1>
              <p className="text-xl text-muted-foreground leading-relaxed mt-2" aria-describedby="journal-main-heading">Express your thoughts and track your emotional journey</p>
            </div>
          </div>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* New Entry Form */}
          <section className="lg:col-span-2" aria-labelledby="new-entry-heading">
            <article className="bg-card rounded-xl shadow-lg border border-border p-10 mb-12">
              <header className="flex items-center gap-4 mb-8">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center border border-primary/20" aria-hidden="true">
                  <Plus className="w-5 h-5 text-primary" strokeWidth={1.5} />
                </div>
                <h2 id="new-entry-heading" className="text-2xl font-bold text-card-foreground">New Journal Entry</h2>
              </header>

              <form onSubmit={(e) => { e.preventDefault(); saveEntry(); }} className="space-y-6">
                <div>
                  <label htmlFor="journal-entry" className="sr-only">
                    Write your journal entry
                  </label>
                  <textarea
                    id="journal-entry"
                    value={newEntry}
                    onChange={(e) => setNewEntry(e.target.value)}
                    placeholder="What's on your mind today? Write about your thoughts, feelings, progress, or anything you'd like to remember..."
                    rows={8}
                    className="w-full p-6 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none"
                    aria-describedby="journal-entry-help"
                    required
                  />
                  <div id="journal-entry-help" className="sr-only">
                    Write about your thoughts, feelings, and progress in your wellness journey
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={saving || !newEntry.trim()}
                    className="flex items-center gap-3 px-8 py-4 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary-foreground focus:ring-offset-2"
                    aria-label={saving ? 'Saving journal entry' : 'Save journal entry'}
                  >
                    {saving ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin" strokeWidth={1.5} aria-hidden="true" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <BookOpen className="w-5 h-5" strokeWidth={1.5} aria-hidden="true" />
                        Save Entry
                      </>
                    )}
                  </button>
                </div>
              </form>
            </article>

            {/* Entries List */}
            <section className="bg-card rounded-xl shadow-lg border border-border" aria-labelledby="entries-heading">
              <header className="p-8 border-b border-border">
                <div className="flex items-center justify-between mb-6">
                  <h2 id="entries-heading" className="text-2xl font-bold text-card-foreground">
                    Entries for {formatDisplayDate(selectedDate)}
                  </h2>
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <Search className="w-5 h-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground" strokeWidth={1.5} aria-hidden="true" />
                      <label htmlFor="search-entries" className="sr-only">Search journal entries</label>
                      <input
                        id="search-entries"
                        type="text"
                        placeholder="Search entries..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-12 pr-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg min-h-[44px]"
                        aria-describedby="search-help"
                      />
                      <div id="search-help" className="sr-only">Search through your journal entries by content</div>
                    </div>
                  </div>
                </div>
              </header>

              <div className="p-8">
                {loading ? (
                  <div className="space-y-6" role="status" aria-live="polite" aria-label="Loading journal entries">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="animate-pulse" aria-hidden="true">
                        <div className="h-6 bg-muted rounded w-1/4 mb-4"></div>
                        <div className="h-6 bg-muted rounded w-full mb-2"></div>
                        <div className="h-6 bg-muted rounded w-3/4"></div>
                      </div>
                    ))}
                    <p className="sr-only">Loading journal entries...</p>
                  </div>
                ) : filteredEntries.length === 0 ? (
                  <div className="text-center py-20" role="status">
                    <div className="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20" aria-hidden="true">
                      <BookOpen className="w-10 h-10 text-primary" strokeWidth={1.5} />
                    </div>
                    <h3 className="text-2xl font-bold text-card-foreground mb-4">
                      {searchQuery ? 'No matching entries found' : 'No entries for this date'}
                    </h3>
                    <p className="text-muted-foreground text-lg">
                      {searchQuery ? 'Try adjusting your search terms.' : 'Start by writing your first journal entry above.'}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-8" role="list" aria-label="Journal entries">
                    {filteredEntries.map((entry) => (
                      <article key={entry.id} className="border-l-4 border-primary pl-6 py-2" role="listitem">
                        <header className="flex items-center justify-between mb-4">
                          <time className="font-semibold text-primary" dateTime={entry.created_at}>
                            {formatDate(entry.created_at)}
                          </time>
                        </header>
                        <div className="text-card-foreground whitespace-pre-wrap leading-relaxed text-lg">
                          {entry.content}
                        </div>
                      </article>
                    ))}
                  </div>
                )}
              </div>
            </section>
          </section>

          {/* Sidebar */}
          <aside className="space-y-8" aria-label="Journal tools and insights">
            {/* Date Picker */}
            <section className="bg-card rounded-xl shadow-lg border border-border p-8" aria-labelledby="date-picker-heading">
              <header className="flex items-center gap-4 mb-6">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center border border-primary/20" aria-hidden="true">
                  <CalendarIcon className="w-5 h-5 text-primary" strokeWidth={1.5} />
                </div>
                <h3 id="date-picker-heading" className="text-xl font-bold text-card-foreground">View Past Entries</h3>
              </header>
              <p className="text-muted-foreground mb-6 leading-relaxed">Select a date to view entries from that day</p>

              <div className="space-y-4">
                <label htmlFor="date-selector" className="block text-base font-semibold text-card-foreground">Select Date</label>
                <input
                  id="date-selector"
                  type="date"
                  value={selectedDate.toISOString().split('T')[0]}
                  onChange={(e) => setSelectedDate(new Date(e.target.value))}
                  max={new Date().toISOString().split('T')[0]}
                  className="w-full p-4 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg min-h-[44px]"
                  aria-describedby="date-help"
                />
                <div id="date-help" className="sr-only">Choose a date to view journal entries from that specific day</div>
              </div>
            </section>

            {/* Journal Stats */}
            <div className="bg-card rounded-xl shadow-lg border border-border p-8">
              <h3 className="text-xl font-bold text-card-foreground mb-6">Journal Insights</h3>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-lg">Today's Entries</span>
                  <span className="font-bold text-card-foreground text-xl">{filteredEntries.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-lg">Selected Date</span>
                  <span className="font-bold text-card-foreground text-xl">
                    {selectedDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  </span>
                </div>
              </div>
            </div>

            {/* Tips */}
            <div className="bg-primary/5 rounded-xl border border-primary/20 p-8">
              <h3 className="text-xl font-bold text-card-foreground mb-6">Journaling Tips</h3>
              <ul className="space-y-4 text-muted-foreground">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></span>
                  <span className="leading-relaxed">Write regularly to build a habit</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></span>
                  <span className="leading-relaxed">Be honest about your feelings</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></span>
                  <span className="leading-relaxed">Track your quit journey progress</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></span>
                  <span className="leading-relaxed">Note triggers and coping strategies</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></span>
                  <span className="leading-relaxed">Celebrate small victories</span>
                </li>
              </ul>
            </div>
          </aside>
        </div>
      </main>
    </div>
  )
}
