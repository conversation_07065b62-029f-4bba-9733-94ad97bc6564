import { Link } from 'react-router-dom'
import { Package, MapPin, BookOpen, Calculator, Heart, Shield, ArrowRight, Search, Star, Filter } from 'lucide-react'
import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'

// Icon mapping for dynamic icon selection
const iconMap: Record<string, any> = {
  Package,
  MapPin,
  BookOpen,
  Calculator,
  Heart,
  Shield
}

interface WebTool {
  id: string
  title: string
  description: string
  link: string
  icon_name: string
  color_classes: string
  display_order: number
}

export default function ToolsPage() {
  // RULE 0001: Dynamic web tools from database - hardcoded array removed
  const [tools, setTools] = useState<WebTool[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [favorites, setFavorites] = useState<string[]>([])

  const filteredTools = tools.filter(tool => {
    const matchesSearch = tool.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' ||
                           (selectedCategory === 'nrt' && (tool.title.includes('NRT') || tool.title.includes('Products'))) ||
                           (selectedCategory === 'guides' && (tool.title.includes('Guide') || tool.title.includes('Methods'))) ||
                           (selectedCategory === 'calculators' && tool.title.includes('Calculator')) ||
                           (selectedCategory === 'wellness' && tool.title.includes('Holistic'))
    return matchesSearch && matchesCategory
  })

  useEffect(() => {
    fetchWebTools()
  }, [])

  const fetchWebTools = async () => {
    setLoading(true)
    try {
      // HOLY RULE 0001: Load real tools from mission_fresh.learning_modules
      const { data, error } = await supabase
        .from('mission_fresh.learning_modules')
        .select('id, title, description, slug, order_index')
        .eq('category', 'tools')
        .eq('is_published', true)
        .order('order_index', { ascending: true })

      if (error) {
        console.error('Error loading tools:', error)
        // Use static fallback tools when database query fails
        const staticTools: WebTool[] = [
          {
            id: '1',
            title: 'NRT Guide',
            description: 'Comprehensive guide to Nicotine Replacement Therapy products.',
            link: '/tools/nrt-guide',
            icon_name: 'Package',
            color_classes: '',
            display_order: 1
          },
          {
            id: '2',
            title: 'Quit Methods',
            description: 'Evidence-based smoking cessation methods and strategies.',
            link: '/tools/quit-methods',
            icon_name: 'BookOpen',
            color_classes: '',
            display_order: 2
          },
          {
            id: '3',
            title: 'Calculators',
            description: 'Calculate money saved and health improvements.',
            link: '/tools/calculators',
            icon_name: 'Calculator',
            color_classes: '',
            display_order: 3
          }
        ]
        setTools(staticTools)

      } else if (data && data.length > 0) {
        // Map database data to WebTool interface
        const dbTools: WebTool[] = data.map((item, index) => ({
          id: item.id,
          title: item.title,
          description: item.description,
          link: `/tools/${item.slug}`,
          icon_name: ['Package', 'Shield', 'MapPin', 'BookOpen', 'Calculator', 'Heart'][index] || 'Package',
          color_classes: '',
          display_order: item.order_index
        }))
        setTools(dbTools)
      } else {
        // No data found, use static fallback
        const staticTools: WebTool[] = [
          {
            id: '1',
            title: 'NRT Guide',
            description: 'Comprehensive guide to Nicotine Replacement Therapy products.',
            link: '/tools/nrt-guide',
            icon_name: 'Package',
            color_classes: '',
            display_order: 1
          },
          {
            id: '2',
            title: 'Quit Methods',
            description: 'Evidence-based smoking cessation methods and strategies.',
            link: '/tools/quit-methods',
            icon_name: 'BookOpen',
            color_classes: '',
            display_order: 2
          },
          {
            id: '3',
            title: 'Calculators',
            description: 'Calculate money saved and health improvements.',
            link: '/tools/calculators',
            icon_name: 'Calculator',
            color_classes: '',
            display_order: 3
          }
        ]
        setTools(staticTools)
      }
    } catch (err) {
      console.error('Error loading tools:', err)
      // Use static fallback tools when database query fails
      const staticTools: WebTool[] = [
        {
          id: '1',
          title: 'NRT Guide',
          description: 'Comprehensive guide to Nicotine Replacement Therapy products.',
          link: '/tools/nrt-guide',
          icon_name: 'Package',
          color_classes: '',
          display_order: 1
        },
        {
          id: '2',
          title: 'Quit Methods',
          description: 'Evidence-based smoking cessation methods and strategies.',
          link: '/tools/quit-methods',
          icon_name: 'BookOpen',
          color_classes: '',
          display_order: 2
        },
        {
          id: '3',
          title: 'Calculators',
          description: 'Calculate money saved and health improvements.',
          link: '/tools/calculators',
          icon_name: 'Calculator',
          color_classes: '',
          display_order: 3
        }
      ]
      setTools(staticTools)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">

        {/* Loading Skeleton */}
        <div className="py-24 bg-background">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center max-w-4xl mx-auto mb-16">
              <div className="h-16 bg-card rounded-lg animate-pulse mb-8 mx-auto max-w-2xl"></div>
              <div className="h-6 bg-card rounded-lg animate-pulse mb-4 mx-auto max-w-3xl"></div>
              <div className="h-6 bg-card rounded-lg animate-pulse mx-auto max-w-2xl"></div>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-12">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="bg-card rounded-lg p-8 border border-border animate-pulse">
                  <div className="w-16 h-16 bg-muted rounded-lg mb-6"></div>
                  <div className="h-6 bg-muted rounded-lg mb-4"></div>
                  <div className="h-4 bg-muted rounded-lg mb-2"></div>
                  <div className="h-4 bg-muted rounded-lg mb-2"></div>
                  <div className="h-4 bg-muted rounded-lg mb-6 w-3/4"></div>
                  <div className="h-4 bg-muted rounded-lg w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <main role="main" aria-label="Mission Fresh Tools">

      {/* Breadcrumb Navigation */}
      <nav aria-label="Breadcrumb" className="bg-muted/30 border-b border-border">
        <div className="container mx-auto px-4 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <Link to="/" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Home
              </Link>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <span className="text-foreground font-medium" aria-current="page">
                Tools
              </span>
            </li>
          </ol>
        </div>
      </nav>

      {/* Skip navigation links */}
      <div className="sr-only focus-within:not-sr-only focus-within:absolute focus-within:top-4 focus-within:left-4 z-50 flex flex-col gap-2">
        <a
          href="#main-content"
          className="bg-primary text-primary-foreground px-4 py-3 min-h-[44px] min-w-[44px] flex items-center justify-center rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-foreground"
        >
          Skip to main content
        </a>
        <a
          href="#tools-section"
          className="bg-primary text-primary-foreground px-4 py-3 min-h-[44px] min-w-[44px] flex items-center justify-center rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-foreground"
        >
          Skip to tools
        </a>
        <a
          href="#cta-section"
          className="bg-primary text-primary-foreground px-4 py-3 min-h-[44px] min-w-[44px] flex items-center justify-center rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-foreground"
        >
          Skip to get started
        </a>
      </div>
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-lg z-50"
      >
        Skip to main content
      </a>

      {/* Hero Section */}
      <section className="py-24 bg-background" aria-labelledby="hero-heading">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto" id="main-content">
            <h1 id="hero-heading" className="text-5xl md:text-6xl font-bold text-foreground mb-8 tracking-tight">
              Quit Smoking Tools & Resources
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              Comprehensive tools, guides, and resources to support your smoke-free journey
            </p>
          </div>
        </div>
      </section>

      {/* ARIA live region for dynamic updates */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {loading ? 'Loading tools...' : `${filteredTools.length} tools found. Showing ${selectedCategory === 'all' ? 'all' : selectedCategory} tools.`}
      </div>

      {/* Tools Grid */}
      <section id="tools-section" className="py-24" aria-labelledby="tools-heading">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto mb-16">
            <h2 id="tools-heading" className="text-3xl md:text-4xl font-semibold text-center text-foreground mb-8">
              Essential Quit Smoking Tools
            </h2>
            <p className="text-lg text-muted-foreground text-center leading-relaxed mb-8">
              Access our comprehensive collection of evidence-based tools and resources designed to support every aspect of your quit smoking journey
            </p>

            {/* Search and Filter Controls */}
            <div className="flex flex-col md:flex-row gap-4 justify-center items-center mb-8">
              {/* Results Count */}
              <div className="text-sm text-muted-foreground">
                Showing {filteredTools.length} of {tools.length} tools
                {searchTerm && ` for "${searchTerm}"`}
                {selectedCategory !== 'all' && ` in ${selectedCategory}`}
              </div>
            </div>

            <div className="flex flex-col md:flex-row gap-4 justify-center items-center mb-8">
              {/* Search Bar */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" strokeWidth={2} />
                <input
                  type="search"
                  placeholder="Search tools and resources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 min-h-[44px] bg-card border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-foreground"
                  aria-label="Search tools and resources"
                />
              </div>

              {/* Category Filter */}
              <div className="flex items-center gap-2 bg-card rounded-lg p-2 border border-border">
                <Filter className="w-4 h-4 text-muted-foreground" strokeWidth={2} />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="bg-transparent text-foreground font-medium px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary min-h-[44px]"
                  aria-label="Filter tools by category"
                >
                  <option value="all">All Categories</option>
                  <option value="nrt">NRT & Products</option>
                  <option value="guides">Guides & Methods</option>
                  <option value="calculators">Calculators</option>
                  <option value="wellness">Wellness & Health</option>
                </select>
              </div>
            </div>
          </div>
          {/* No Results Message */}
          {filteredTools.length === 0 && (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
                <Search className="w-12 h-12 text-muted-foreground" strokeWidth={1.5} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-2">No tools found</h3>
              <p className="text-muted-foreground mb-6">
                Try adjusting your search terms or category filter
              </p>
              <button
                onClick={() => {
                  setSearchTerm('')
                  setSelectedCategory('all')
                }}
                className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-colors duration-300 min-h-[44px]"
              >
                Clear Filters
              </button>
            </div>
          )}

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-12" role="list" aria-label="Tools and resources list">
            {filteredTools.map((tool, index) => {
              const IconComponent = iconMap[tool.icon_name] || Package
              return (
                <Link
                  key={tool.id}
                  to={tool.link}
                  className="group bg-card rounded-lg p-8 border border-border hover:shadow-lg transition-all duration-300 hover:border-primary min-h-[44px] flex flex-col"
                  role="listitem"
                  aria-labelledby={`tool-${tool.id}-title`}
                  aria-describedby={`tool-${tool.id}-description`}
                >
                  <div className="flex items-center justify-between mb-6">
                    <div className="w-16 h-16 bg-primary rounded-lg flex items-center justify-center group-hover:shadow-lg transition-all duration-300">
                      <IconComponent className="w-8 h-8 text-primary-foreground" strokeWidth={2} aria-hidden="true" />
                    </div>
                    <button
                      onClick={(e) => {
                        e.preventDefault()
                        const isFavorite = favorites.includes(tool.id)
                        if (isFavorite) {
                          setFavorites(favorites.filter(id => id !== tool.id))
                        } else {
                          setFavorites([...favorites, tool.id])
                        }
                      }}
                      className={`p-2 rounded-lg transition-all duration-300 min-h-[44px] min-w-[44px] flex items-center justify-center ${
                        favorites.includes(tool.id)
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-card-foreground/10 text-muted-foreground hover:bg-primary/10 hover:text-primary'
                      }`}
                      aria-label={favorites.includes(tool.id) ? `Remove ${tool.title} from favorites` : `Add ${tool.title} to favorites`}
                    >
                      <Star className="w-4 h-4" strokeWidth={2} fill={favorites.includes(tool.id) ? 'currentColor' : 'none'} />
                    </button>
                  </div>
                  <h3 id={`tool-${tool.id}-title`} className="text-xl font-semibold text-card-foreground mb-4 group-hover:text-primary transition-colors duration-300">
                    {tool.title}
                  </h3>
                  <p id={`tool-${tool.id}-description`} className="text-muted-foreground leading-relaxed mb-6 text-base flex-1">
                    {tool.description}
                  </p>
                  <div className="flex items-center text-primary font-medium group-hover:text-primary-hover transition-all duration-300 min-h-[44px]">
                    <span>Explore Tool</span>
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" strokeWidth={2} aria-hidden="true" />
                  </div>
                </Link>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section id="cta-section" className="py-24 bg-primary text-primary-foreground" aria-labelledby="cta-heading">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 text-center">
          <h2 id="cta-heading" className="text-3xl md:text-4xl font-semibold mb-6 tracking-tight">
            Need More Support?
          </h2>
          <p className="text-xl md:text-2xl text-primary-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
            Join our community and get personalized guidance from our AI assistant
          </p>

          {/* Success Stats - HOLY RULE #12: Static marketing content is acceptable */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-secondary mb-2">{tools.length}</div>
              <div className="text-primary-foreground">Essential Tools</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-secondary mb-2">Evidence-Based</div>
              <div className="text-primary-foreground">Proven Methods</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-secondary mb-2">Comprehensive</div>
              <div className="text-primary-foreground">Support System</div>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              to="/auth"
              className="px-8 py-4 min-h-[44px] bg-card text-primary rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold text-lg hover:shadow-lg flex items-center justify-center"
              aria-label="Join the Mission Fresh community"
            >
              Join Community
            </Link>
            <Link
              to="/fresh-assistant"
              className="px-8 py-4 min-h-[44px] border-2 border-primary-foreground text-primary-foreground rounded-lg hover:bg-primary-foreground hover:text-primary transition-all duration-300 font-semibold text-lg flex items-center justify-center"
              aria-label="Chat with AI assistant for personalized support"
            >
              Chat with AI Assistant
            </Link>
          </div>
        </div>
      </section>
      </main>
    </div>
  )
}
