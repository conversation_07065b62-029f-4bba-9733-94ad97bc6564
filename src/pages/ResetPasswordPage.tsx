import { useState, useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Eye, EyeOff, Lock, CheckCircle } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import Logo from '../components/Logo'

export default function ResetPasswordPage() {
  const { updatePassword, user } = useAuth()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [passwordError, setPasswordError] = useState('')
  const [confirmPasswordError, setConfirmPasswordError] = useState('')

  // Check if user is authenticated (required for password reset)
  useEffect(() => {
    if (!user) {
      // If no user, redirect to auth page with error message
      navigate('/auth?error=session_expired&message=Please sign in again to reset your password')
    }
  }, [user, navigate])

  const validatePassword = (password: string) => {
    return password.length >= 8
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setSuccess('')
    setPasswordError('')
    setConfirmPasswordError('')

    // Validation
    if (!validatePassword(password)) {
      setPasswordError('Password must be at least 8 characters long')
      setLoading(false)
      return
    }

    if (password !== confirmPassword) {
      setConfirmPasswordError('Passwords do not match')
      setLoading(false)
      return
    }

    try {
      if (updatePassword) {
        const { error } = await updatePassword(password)
        
        if (error) {
          setError(error.message)
        } else {
          setSuccess('Password updated successfully!')
          setTimeout(() => navigate('/dashboard'), 2000)
        }
      } else {
        setError('Password update functionality is not available')
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return null // Will redirect via useEffect
  }

  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-0 left-0 w-72 h-72 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-accent/10 rounded-full blur-3xl"></div>

      <div className="min-h-screen flex items-center justify-center p-6 relative">
        <div className="max-w-lg w-full relative z-10">
          <main className="auth-form rounded-2xl shadow-2xl p-12 backdrop-blur-sm">
            {/* Logo and Header */}
            <header className="text-center mb-12">
              <div className="flex justify-center mb-8">
                <Logo size="lg" showText={false} />
              </div>
              <h1 className="text-3xl font-bold text-card-foreground mb-4">
                Reset Your Password
              </h1>
              <p className="text-lg text-muted-foreground leading-relaxed">
                Enter your new password below
              </p>
            </header>

            {/* Reset Password Form */}
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* New Password Field */}
              <div>
                <label htmlFor="password" className="block text-base font-semibold text-card-foreground mb-4">
                  New Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-muted-foreground" aria-hidden="true" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value)
                      setPasswordError('')
                    }}
                    className={`auth-input block w-full pl-12 pr-12 py-4 text-base rounded-xl border-2 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary ${
                      passwordError ? 'border-destructive' : 'border-border focus:border-primary'
                    }`}
                    placeholder="Enter your new password"
                    aria-describedby={passwordError ? 'password-error' : undefined}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-4 flex items-center min-h-[44px] min-w-[44px] justify-center"
                    onClick={() => setShowPassword(!showPassword)}
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-muted-foreground hover:text-foreground transition-colors" />
                    ) : (
                      <Eye className="h-5 w-5 text-muted-foreground hover:text-foreground transition-colors" />
                    )}
                  </button>
                </div>
                {passwordError && (
                  <p id="password-error" className="mt-2 text-sm text-destructive" role="alert">
                    {passwordError}
                  </p>
                )}
              </div>

              {/* Confirm Password Field */}
              <div>
                <label htmlFor="confirmPassword" className="block text-base font-semibold text-card-foreground mb-4">
                  Confirm New Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-muted-foreground" aria-hidden="true" />
                  </div>
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    value={confirmPassword}
                    onChange={(e) => {
                      setConfirmPassword(e.target.value)
                      setConfirmPasswordError('')
                    }}
                    className={`auth-input block w-full pl-12 pr-12 py-4 text-base rounded-xl border-2 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary ${
                      confirmPasswordError ? 'border-destructive' : 'border-border focus:border-primary'
                    }`}
                    placeholder="Confirm your new password"
                    aria-describedby={confirmPasswordError ? 'confirm-password-error' : undefined}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-4 flex items-center min-h-[44px] min-w-[44px] justify-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-5 w-5 text-muted-foreground hover:text-foreground transition-colors" />
                    ) : (
                      <Eye className="h-5 w-5 text-muted-foreground hover:text-foreground transition-colors" />
                    )}
                  </button>
                </div>
                {confirmPasswordError && (
                  <p id="confirm-password-error" className="mt-2 text-sm text-destructive" role="alert">
                    {confirmPasswordError}
                  </p>
                )}
              </div>

              {/* Error Message */}
              {error && (
                <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg" role="alert">
                  <p className="text-sm text-destructive">{error}</p>
                </div>
              )}

              {/* Success Message */}
              {success && (
                <div className="p-4 bg-accent border border-border rounded-lg" role="alert">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-primary" />
                    <p className="text-sm text-foreground">{success}</p>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={loading}
                className="auth-button w-full py-4 px-6 text-base font-semibold rounded-xl transition-all duration-300 min-h-[56px] flex items-center justify-center gap-3 focus:outline-none focus:ring-2 focus:ring-primary-foreground disabled:opacity-50"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-foreground"></div>
                    Updating Password...
                  </>
                ) : (
                  'Update Password'
                )}
              </button>
            </form>

            {/* Back to Dashboard */}
            <div className="mt-8 text-center">
              <button
                type="button"
                onClick={() => navigate('/dashboard')}
                className="text-sm text-muted-foreground hover:text-primary transition-colors min-h-[44px] py-2 px-1"
              >
                Back to Dashboard
              </button>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
