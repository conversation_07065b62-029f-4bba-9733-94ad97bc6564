import { useState, useEffect } from 'react'
import { Mail, Lock, Eye, EyeOff, Chrome, Github, Apple, Loader2 } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate, useSearchParams } from 'react-router-dom'
import Logo from '../components/Logo'

export default function AuthPage() {
  const { signIn, signUp, resetPassword, user } = useAuth()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const [isSignUp, setIsSignUp] = useState(false)
  const [isPasswordReset, setIsPasswordReset] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [emailError, setEmailError] = useState('')
  const [passwordError, setPasswordError] = useState('')
  const [confirmPasswordError, setConfirmPasswordError] = useState('')
  const [passwordStrength, setPasswordStrength] = useState(0)
  const [loginAttempts, setLoginAttempts] = useState(0)
  const [isBlocked, setIsBlocked] = useState(false)
  const [blockTimeRemaining, setBlockTimeRemaining] = useState(0)
  const [emailVerificationSent, setEmailVerificationSent] = useState(false)
  const [resendCooldown, setResendCooldown] = useState(0)
  const [acceptedTerms, setAcceptedTerms] = useState(false)
  const [termsError, setTermsError] = useState('')

  // Rate limiting and block timer management
  useEffect(() => {
    const storedAttempts = localStorage.getItem('loginAttempts')
    const storedBlockTime = localStorage.getItem('blockUntil')

    if (storedAttempts) {
      setLoginAttempts(parseInt(storedAttempts))
    }

    if (storedBlockTime) {
      const blockUntil = parseInt(storedBlockTime)
      const now = Date.now()

      if (now < blockUntil) {
        setIsBlocked(true)
        setBlockTimeRemaining(Math.ceil((blockUntil - now) / 1000))

        // Start countdown timer
        const timer = setInterval(() => {
          const remaining = Math.ceil((blockUntil - Date.now()) / 1000)
          if (remaining <= 0) {
            setIsBlocked(false)
            setBlockTimeRemaining(0)
            setLoginAttempts(0)
            localStorage.removeItem('loginAttempts')
            localStorage.removeItem('blockUntil')
            clearInterval(timer)
          } else {
            setBlockTimeRemaining(remaining)
          }
        }, 1000)

        return () => clearInterval(timer)
      } else {
        // Block time expired, clear stored data
        localStorage.removeItem('loginAttempts')
        localStorage.removeItem('blockUntil')
      }
    }
  }, [])

  // Keyboard navigation support
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Escape key to clear errors and reset form state
      if (e.key === 'Escape') {
        setError('')
        setSuccess('')
        setEmailError('')
        setPasswordError('')
        setConfirmPasswordError('')
        setTermsError('')
        if (emailVerificationSent) {
          setEmailVerificationSent(false)
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [emailVerificationSent])

  // Check URL parameters to set initial mode
  useEffect(() => {
    const mode = searchParams.get('mode')
    const action = searchParams.get('action')

    if (mode === 'signin' || action === 'signin') {
      setIsSignUp(false)
    } else if (mode === 'signup' || action === 'signup') {
      setIsSignUp(true)
    }
  }, [searchParams])

  // Redirect if user is already authenticated
  if (user) {
    navigate('/dashboard')
    return null
  }

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePassword = (password: string) => {
    const requirements = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[^A-Za-z0-9]/.test(password)
    }

    // Require at least 4 out of 5 criteria for strong password
    const metCriteria = Object.values(requirements).filter(Boolean).length
    return metCriteria >= 4 && requirements.length
  }

  const getPasswordRequirements = (password: string) => {
    return [
      { text: 'At least 8 characters', met: password.length >= 8 },
      { text: 'One uppercase letter (A-Z)', met: /[A-Z]/.test(password) },
      { text: 'One lowercase letter (a-z)', met: /[a-z]/.test(password) },
      { text: 'One number (0-9)', met: /[0-9]/.test(password) },
      { text: 'One special character (!@#$%^&*)', met: /[^A-Za-z0-9]/.test(password) }
    ]
  }

  const calculatePasswordStrength = (password: string) => {
    let strength = 0
    if (password.length >= 8) strength += 1
    if (/[A-Z]/.test(password)) strength += 1
    if (/[a-z]/.test(password)) strength += 1
    if (/[0-9]/.test(password)) strength += 1
    if (/[^A-Za-z0-9]/.test(password)) strength += 1
    return strength
  }

  const getPasswordStrengthText = (strength: number) => {
    switch (strength) {
      case 0:
      case 1: return 'Weak'
      case 2:
      case 3: return 'Medium'
      case 4:
      case 5: return 'Strong'
      default: return 'Weak'
    }
  }

  const getPasswordStrengthColor = (strength: number) => {
    switch (strength) {
      case 0:
      case 1: return 'text-destructive'
      case 2:
      case 3: return 'text-warning'
      case 4:
      case 5: return 'text-success'
      default: return 'text-destructive'
    }
  }

  const handleSocialAuth = async (provider: 'google' | 'github' | 'apple') => {
    setLoading(true)
    setError('')

    try {
      // In a real implementation, this would integrate with Supabase social auth
      // For now, show a message that it's not yet implemented
      setError(`${provider.charAt(0).toUpperCase() + provider.slice(1)} authentication is not yet configured. Please use email/password authentication.`)
    } catch (err) {
      setError(`Failed to authenticate with ${provider}. Please try again.`)
    } finally {
      setLoading(false)
    }
  }

  const handleResendVerification = async () => {
    if (resendCooldown > 0) return

    setLoading(true)
    setError('')

    try {
      // In a real implementation, this would resend the verification email
      setSuccess('Verification email resent! Please check your inbox.')
      setResendCooldown(60) // 60 second cooldown

      // Start cooldown timer
      const timer = setInterval(() => {
        setResendCooldown(prev => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } catch (err) {
      setError('Failed to resend verification email. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Check if user is blocked due to too many failed attempts
    if (isBlocked) {
      setError(`Too many failed attempts. Please wait ${blockTimeRemaining} seconds before trying again.`)
      return
    }

    setLoading(true)
    setError('')
    setSuccess('')
    setEmailError('')
    setPasswordError('')
    setTermsError('')

    // Validation
    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address')
      setLoading(false)
      return
    }

    if (!validatePassword(password)) {
      setPasswordError('Password must meet at least 4 of the 5 security requirements')
      setLoading(false)
      return
    }

    if (isSignUp && password !== confirmPassword) {
      setPasswordError('Passwords do not match')
      setLoading(false)
      return
    }

    if (isSignUp && !acceptedTerms) {
      setTermsError('You must accept the Terms of Service and Privacy Policy to create an account')
      setLoading(false)
      return
    }

    try {
      const { error: authError } = isSignUp
        ? await signUp(email, password)
        : await signIn(email, password)

      if (authError) {
        // Handle failed login attempt (only for sign in, not sign up)
        if (!isSignUp) {
          const newAttempts = loginAttempts + 1
          setLoginAttempts(newAttempts)
          localStorage.setItem('loginAttempts', newAttempts.toString())

          if (newAttempts >= 5) {
            // Block user for 15 minutes after 5 failed attempts
            const blockUntil = Date.now() + (15 * 60 * 1000)
            localStorage.setItem('blockUntil', blockUntil.toString())
            setIsBlocked(true)
            setBlockTimeRemaining(15 * 60)
            setError(`Too many failed attempts. Account temporarily blocked for 15 minutes.`)
          } else {
            const remainingAttempts = 5 - newAttempts
            setError(`${authError.message} (${remainingAttempts} attempts remaining)`)
          }
        } else {
          setError(authError.message)
        }
      } else {
        // Successful authentication - reset attempts
        setLoginAttempts(0)
        localStorage.removeItem('loginAttempts')
        localStorage.removeItem('blockUntil')

        if (isSignUp) {
          // For signup, show email verification message
          setEmailVerificationSent(true)
          setSuccess('Account created! Please check your email and click the verification link to complete your registration.')
          // Don't redirect immediately for signup - wait for email verification
        } else {
          // For signin, redirect to dashboard
          setSuccess('Welcome back!')
          setTimeout(() => navigate('/dashboard'), 1500)
        }
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordReset = async () => {
    if (!email) {
      setEmailError('Please enter your email address first')
      return
    }

    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address')
      return
    }

    setLoading(true)
    setError('')
    setSuccess('')
    setEmailError('')

    try {
      const { error } = await resetPassword(email)

      if (error) {
        setError(error.message)
      } else {
        setSuccess('Password reset email sent! Check your inbox for instructions.')
        setIsPasswordReset(true)
      }
    } catch (err) {
      setError('Failed to send password reset email. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted relative overflow-hidden">
      <main role="main" aria-label="Mission Fresh Authentication">

      {/* Breadcrumb Navigation */}
      <nav aria-label="Breadcrumb" className="absolute top-0 left-0 right-0 z-40 bg-background/80 backdrop-blur-sm border-b border-border">
        <div className="container mx-auto px-4 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a href="/" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Home
              </a>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <span className="text-foreground font-medium" aria-current="page">
                {isSignUp ? 'Sign Up' : 'Sign In'}
              </span>
            </li>
          </ol>
        </div>
      </nav>

      {/* Skip navigation links - hidden by default, only visible on keyboard focus */}
      <a
        href="#auth-form"
        className="sr-only focus:not-sr-only focus:absolute focus:top-20 focus:left-4 bg-primary text-primary-foreground px-4 py-3 min-h-[44px] min-w-[44px] flex items-center justify-center rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-primary-foreground"
      >
        Skip to authentication form
      </a>

      {/* ARIA live region for form feedback */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {error && `Error: ${error}`}
        {success && `Success: ${success}`}
        {loading && 'Processing authentication...'}
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-0 left-0 w-72 h-72 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-accent/10 rounded-full blur-3xl"></div>

      <div className="min-h-screen flex items-center justify-center p-6 pt-24 relative">

        <div className="max-w-lg w-full relative z-10">
          <main className="auth-form rounded-2xl shadow-2xl p-12 backdrop-blur-sm">
          {/* Logo and Header */}
          <header className="text-center mb-12">
            <div className="flex justify-center mb-8">
              <Logo size="xl" showText={true} linkTo="/" />
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-2">
              {isSignUp ? 'Create Your Account' : 'Welcome Back'}
            </h1>
            <p className="text-xl text-muted-foreground leading-relaxed">
              {isSignUp ? 'Start your smoke-free journey today' : 'Continue your wellness journey'}
            </p>
          </header>

          {/* Auth Form */}
          <form id="auth-form" onSubmit={handleSubmit} className="space-y-8" role="form" aria-label={isSignUp ? "Create account form" : "Sign in form"}>
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-base font-semibold text-card-foreground mb-4">
                Email
              </label>
              <div className="relative">
                <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-muted-foreground" strokeWidth={1.5} />
                <input
                  type="email"
                  id="email"
                  name="email"
                  autoComplete="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value)
                    // Clear error on change
                    if (emailError) setEmailError('')
                  }}
                  onBlur={() => {
                    // Real-time validation on blur
                    if (email && !validateEmail(email)) {
                      setEmailError('Please enter a valid email address')
                    }
                  }}
                  onKeyDown={(e) => {
                    // Enter key to move to next field or submit
                    if (e.key === 'Enter') {
                      e.preventDefault()
                      const passwordField = document.getElementById('password')
                      if (passwordField) {
                        passwordField.focus()
                      }
                    }
                  }}
                  className="auth-input pl-12 pr-4 py-4 min-h-[44px] rounded-lg focus:ring-2 focus:ring-primary text-lg"
                  placeholder="Enter your email"
                  required
                  aria-describedby={emailError ? "email-error" : undefined}
                  aria-invalid={emailError ? "true" : "false"}
                />
              </div>
              {emailError && (
                <div id="email-error" className="mt-2 text-sm text-destructive" role="alert" aria-live="polite">
                  {emailError}
                </div>
              )}
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-base font-semibold text-card-foreground mb-4">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-muted-foreground" strokeWidth={1.5} />
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  autoComplete={isSignUp ? "new-password" : "current-password"}
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value)
                    setPasswordStrength(calculatePasswordStrength(e.target.value))
                    // Clear error on change
                    if (passwordError) setPasswordError('')
                  }}
                  onBlur={() => {
                    // Real-time validation on blur
                    if (password && !validatePassword(password)) {
                      setPasswordError('Password must meet at least 4 of the 5 security requirements')
                    }
                  }}
                  onKeyDown={(e) => {
                    // Enter key to move to next field or submit
                    if (e.key === 'Enter') {
                      e.preventDefault()
                      if (isSignUp) {
                        const confirmPasswordField = document.getElementById('confirmPassword')
                        if (confirmPasswordField) {
                          confirmPasswordField.focus()
                        }
                      } else {
                        // For sign in, submit the form
                        const form = document.getElementById('auth-form') as HTMLFormElement
                        if (form) {
                          form.requestSubmit()
                        }
                      }
                    }
                  }}
                  className="auth-input pl-12 pr-12 py-4 min-h-[44px] rounded-lg focus:ring-2 focus:ring-primary text-lg"
                  placeholder="Enter your password"
                  required
                  aria-describedby={passwordError ? "password-error" : undefined}
                  aria-invalid={passwordError ? "true" : "false"}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-primary transition-colors duration-300 min-h-[44px] min-w-[44px] flex items-center justify-center"
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? <EyeOff className="w-6 h-6" strokeWidth={1.5} aria-hidden="true" /> : <Eye className="w-6 h-6" strokeWidth={1.5} aria-hidden="true" />}
                </button>
              </div>
              {passwordError && (
                <div id="password-error" className="mt-2 text-sm text-destructive" role="alert" aria-live="polite">
                  {passwordError}
                </div>
              )}
              {isSignUp && password && (
                <div className="mt-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">Password strength:</span>
                    <span className={`text-sm font-medium ${getPasswordStrengthColor(passwordStrength)}`}>
                      {getPasswordStrengthText(passwordStrength)}
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        passwordStrength <= 1 ? 'bg-destructive' :
                        passwordStrength <= 3 ? 'bg-warning' : 'bg-success'
                      }`}
                      style={{ width: `${(passwordStrength / 5) * 100}%` }}
                    />
                  </div>

                  {/* Password Requirements */}
                  <div className="mt-4">
                    <p className="text-sm text-muted-foreground mb-2">Password requirements:</p>
                    <ul className="space-y-1">
                      {getPasswordRequirements(password).map((req, index) => (
                        <li key={index} className="flex items-center gap-2 text-xs">
                          <div className={`w-2 h-2 rounded-full ${req.met ? 'bg-success' : 'bg-muted-foreground'}`} />
                          <span className={req.met ? 'text-success' : 'text-muted-foreground'}>
                            {req.text}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
            </div>

            {/* Confirm Password Field for Sign Up */}
            {isSignUp && (
              <div>
                <label htmlFor="confirmPassword" className="block text-base font-semibold text-card-foreground mb-4">
                  Confirm Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-muted-foreground" strokeWidth={1.5} />
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    autoComplete="new-password"
                    value={confirmPassword}
                    onChange={(e) => {
                      setConfirmPassword(e.target.value)
                      if (e.target.value && password && e.target.value !== password) {
                        setConfirmPasswordError('Passwords do not match')
                      } else {
                        setConfirmPasswordError('')
                      }
                    }}
                    onBlur={() => {
                      // Real-time validation on blur
                      if (confirmPassword && password && confirmPassword !== password) {
                        setConfirmPasswordError('Passwords do not match')
                      }
                    }}
                    onKeyDown={(e) => {
                      // Enter key to submit form
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        const form = document.getElementById('auth-form') as HTMLFormElement
                        if (form) {
                          form.requestSubmit()
                        }
                      }
                    }}
                    className="auth-input pl-12 pr-4 py-4 min-h-[44px] rounded-lg focus:ring-2 focus:ring-primary text-lg"
                    placeholder="Confirm your password"
                    required={isSignUp}
                    aria-describedby={confirmPasswordError ? "confirm-password-error" : undefined}
                    aria-invalid={confirmPasswordError ? "true" : "false"}
                  />
                </div>
                {confirmPasswordError && (
                  <div id="confirm-password-error" className="mt-2 text-sm text-destructive" role="alert" aria-live="polite">
                    {confirmPasswordError}
                  </div>
                )}
              </div>
            )}

            {/* General Error Message */}
            {error && (
              <div id="auth-error" className="p-4 bg-muted border border-border rounded-lg" role="alert" aria-live="polite">
                <p className="text-sm text-foreground">{error}</p>
              </div>
            )}

            {/* Success Message */}
            {success && (
              <div className="p-4 bg-accent border border-border rounded-lg" role="alert" aria-live="polite">
                <p className="text-sm text-foreground">{success}</p>
              </div>
            )}

            {/* Email Verification Section */}
            {emailVerificationSent && (
              <div className="p-4 bg-accent border border-border rounded-lg" role="alert" aria-live="polite">
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-foreground mb-2">Check Your Email</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    We've sent a verification link to <strong>{email}</strong>.
                    Please click the link in your email to verify your account.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <button
                      type="button"
                      onClick={handleResendVerification}
                      disabled={loading || resendCooldown > 0}
                      className="btn-secondary px-4 py-2 rounded-lg font-medium disabled:opacity-50 min-h-[44px] flex items-center justify-center gap-2"
                      aria-label="Resend verification email"
                    >
                      {loading && (
                        <Loader2 className="w-4 h-4 animate-spin" strokeWidth={2} />
                      )}
                      {resendCooldown > 0
                        ? `Resend in ${resendCooldown}s`
                        : loading
                          ? 'Sending...'
                          : 'Resend Email'
                      }
                    </button>
                    <button
                      type="button"
                      onClick={() => setEmailVerificationSent(false)}
                      className="btn-primary px-4 py-2 rounded-lg font-medium min-h-[44px]"
                      aria-label="Go back to sign in"
                    >
                      Back to Sign In
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Forgot Password Link */}
            {!isSignUp && (
              <div className="text-right">
                <button
                  type="button"
                  onClick={handlePasswordReset}
                  disabled={loading}
                  className="text-primary hover:text-primary-hover font-medium transition-colors duration-300 text-sm min-h-[44px] px-2 py-2 flex items-center justify-end gap-2 focus:outline-none focus:ring-2 focus:ring-primary rounded disabled:opacity-50"
                  aria-label="Reset forgotten password"
                >
                  {loading && (
                    <Loader2 className="w-4 h-4 animate-spin" strokeWidth={2} />
                  )}
                  {loading ? 'Sending...' : 'Forgot your password?'}
                </button>
              </div>
            )}

            {/* Terms and Privacy Policy Acceptance (Sign Up Only) */}
            {isSignUp && (
              <div className="space-y-2">
                <div className="flex items-start gap-3">
                  <input
                    type="checkbox"
                    id="accept-terms"
                    checked={acceptedTerms}
                    onChange={(e) => setAcceptedTerms(e.target.checked)}
                    className="mt-1 w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary focus:ring-2"
                    aria-describedby="terms-error"
                  />
                  <label htmlFor="accept-terms" className="text-sm text-muted-foreground leading-relaxed">
                    I agree to the{' '}
                    <a
                      href="/terms"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:text-primary-hover underline font-medium"
                    >
                      Terms of Service
                    </a>
                    {' '}and{' '}
                    <a
                      href="/privacy"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:text-primary-hover underline font-medium"
                    >
                      Privacy Policy
                    </a>
                  </label>
                </div>

                {/* Terms Error Message */}
                {termsError && (
                  <div id="terms-error" className="text-sm text-destructive" role="alert" aria-live="polite">
                    {termsError}
                  </div>
                )}
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading || isBlocked}
              className="auth-button w-full px-8 py-4 min-h-[44px] rounded-lg disabled:opacity-50 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
              aria-describedby={error ? "auth-error" : undefined}
            >
              {loading && (
                <Loader2 className="w-5 h-5 animate-spin" strokeWidth={2} />
              )}
              {isBlocked
                ? `Blocked (${Math.floor(blockTimeRemaining / 60)}:${(blockTimeRemaining % 60).toString().padStart(2, '0')})`
                : loading
                  ? 'Please wait...'
                  : (isSignUp ? 'Create Account' : 'Sign In')
              }
            </button>
          </form>

          {/* Social Authentication */}
          <div className="mt-8">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-border"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-card text-muted-foreground">Or continue with</span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-3 gap-3">
              {/* Google */}
              <button
                type="button"
                onClick={() => handleSocialAuth('google')}
                disabled={loading || isBlocked}
                className="w-full inline-flex justify-center items-center px-4 py-3 border border-border rounded-lg bg-background hover:bg-muted transition-colors duration-300 disabled:opacity-50 min-h-[44px]"
                aria-label="Sign in with Google"
              >
                {loading ? (
                  <Loader2 className="w-5 h-5 text-muted-foreground animate-spin" strokeWidth={1.5} />
                ) : (
                  <Chrome className="w-5 h-5 text-muted-foreground" strokeWidth={1.5} />
                )}
              </button>

              {/* GitHub */}
              <button
                type="button"
                onClick={() => handleSocialAuth('github')}
                disabled={loading || isBlocked}
                className="w-full inline-flex justify-center items-center px-4 py-3 border border-border rounded-lg bg-background hover:bg-muted transition-colors duration-300 disabled:opacity-50 min-h-[44px]"
                aria-label="Sign in with GitHub"
              >
                {loading ? (
                  <Loader2 className="w-5 h-5 text-muted-foreground animate-spin" strokeWidth={1.5} />
                ) : (
                  <Github className="w-5 h-5 text-muted-foreground" strokeWidth={1.5} />
                )}
              </button>

              {/* Apple */}
              <button
                type="button"
                onClick={() => handleSocialAuth('apple')}
                disabled={loading || isBlocked}
                className="w-full inline-flex justify-center items-center px-4 py-3 border border-border rounded-lg bg-background hover:bg-muted transition-colors duration-300 disabled:opacity-50 min-h-[44px]"
                aria-label="Sign in with Apple"
              >
                {loading ? (
                  <Loader2 className="w-5 h-5 text-muted-foreground animate-spin" strokeWidth={1.5} />
                ) : (
                  <Apple className="w-5 h-5 text-muted-foreground" strokeWidth={1.5} />
                )}
              </button>
            </div>
          </div>

          {/* Toggle Auth Mode */}
          <div id="auth-toggle" className="mt-10 text-center">
            <p className="text-muted-foreground text-lg">
              {isSignUp ? 'Already have an account?' : "Don't have an account?"}
              <button
                onClick={() => setIsSignUp(!isSignUp)}
                className="ml-2 text-primary hover:text-primary-hover font-semibold transition-colors duration-300 min-h-[44px] py-2 px-1"
                aria-label={isSignUp ? 'Switch to sign in' : 'Switch to sign up'}
              >
                {isSignUp ? 'Sign In' : 'Sign Up'}
              </button>
            </p>

            {/* Forgot Password Link for Sign In */}
            {!isSignUp && (
              <div className="mt-4">
                <button
                  type="button"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors min-h-[44px] py-2 px-1 disabled:opacity-50"
                  onClick={handlePasswordReset}
                  disabled={loading}
                >
                  Forgot your password?
                </button>
              </div>
            )}
          </div>
          </main>
        </div>
      </div>
      </main>
    </div>
  )
}
