/*
  ======================================================================================
  MISSION FRESH - COLORS AND FONTS ONLY - HOLY RULE 0003 COMPLIANCE
  Apple-style elegance - Only ONE shade per color across entire app
  NO sizing, spacing, or layout properties allowed
  ======================================================================================
*/

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Border Radius - Apple Style Elegance */
    --radius: 0.75rem;

    /* Base Colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* Primary Brand Colors - ONLY ONE GREEN ALLOWED */
    --primary: 160 84.2% 39.4%;
    --primary-foreground: 0 0% 100%;
    
    /* Secondary Colors - ONLY ONE SHADE */
    --secondary: 0 0% 100%;
    --secondary-foreground: 222.2 84% 4.9%;
    --secondary-hover: 0 0% 96%;
    
    /* Muted Colors */
    --muted: 0 0% 98%;
    --muted-foreground: 215.4 16.3% 35%;
    --muted-subtle: 0 0% 98%;
    
    /* Accent Colors - ONLY ONE SHADE */
    --accent: 0 0% 100%;
    --accent-foreground: 222.2 84% 4.9%;
    
    /* Status Colors - ONLY ONE SHADE PER COLOR */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --destructive-subtle: 0 84.2% 98%;
    --success: 160 84.2% 39.4%;
    --success-foreground: 0 0% 100%;
    --success-subtle: 160 84.2% 98%;
    --warning: 35 85% 55%;
    --warning-foreground: 0 0% 100%;
    --warning-subtle: 35 85% 98%;
    --info: 212 85% 60%;
    --info-foreground: 0 0% 100%;
    
    /* UI Element Colors */
    --border: 214.3 31.8% 91.4%;
    --input: 0 0% 100%;
    --ring: 160 84.2% 39.4%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Chart Color - SINGLE BRAND GREEN ONLY */
    --chart: 160 84.2% 39.4%;
    
    /* Apple-Style Shadow System - Consistent across app */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --shadow-primary: 0 4px 14px 0 hsla(var(--primary), 0.3);
  }

  /* Font Character Styles Only */
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: Inter, system-ui, -apple-system, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11", "ss01", "ss03";
  }

  /* Header Background Color Only */
  nav, header {
    background-color: hsl(var(--background));
  }

  /* UNIFIED BUTTON SYSTEM - ONLY BRAND GREEN ALLOWED - COLORS ONLY */
  .btn-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  .btn-primary:hover {
    background-color: hsl(var(--primary));
  }

  .btn-primary:focus {
    outline-color: hsl(var(--primary));
  }

  .btn-secondary {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
    border-color: hsl(var(--border));
  }

  .btn-secondary:hover {
    background-color: hsl(var(--secondary-hover));
  }

  /* NAVIGATION LINK STYLES - COLORS ONLY */
  .nav-link:hover {
    color: hsl(var(--primary));
  }

  .nav-link:focus {
    outline-color: hsl(var(--primary));
  }

  /* CARD COMPONENT STYLES - COLORS ONLY */
  .card {
    background-color: hsl(var(--card));
    border-color: hsl(var(--border));
  }

  /* ICON CONTAINER STYLES - COLORS ONLY */
  .icon-container {
    background-color: hsl(var(--primary));
  }

  .icon-container-round {
    background-color: hsl(var(--card));
    border-color: hsl(var(--border));
  }

  /* TEXT COMPONENT STYLES - COLORS ONLY */
  .heading-primary {
    color: hsl(var(--foreground));
  }

  .heading-secondary {
    color: hsl(var(--foreground));
  }

  .text-muted {
    color: hsl(var(--muted-foreground));
  }

  /* ACCESSIBILITY ENHANCEMENTS - COLORS ONLY */
  .btn-primary:focus,
  .btn-secondary:focus {
    outline-color: hsl(var(--primary));
  }

  .card:focus-within {
    outline-color: hsl(var(--primary));
  }



  /* Auth form styling - Apple System Preferences style */
  .auth-form {
    background: hsl(var(--card));
    border-color: hsl(var(--border));
  }

  /* Auth input styling */
  .auth-input {
    border-color: hsl(var(--border));
    background: hsl(var(--background));
    color: hsl(var(--foreground));
  }

  .auth-input:focus {
    border-color: hsl(var(--primary));
  }

  .auth-input::placeholder {
    color: hsl(var(--muted-foreground));
  }

  /* Auth button styling */
  .auth-button {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  .auth-button:hover:not(:disabled) {
    background: hsl(var(--primary));
  }

  .auth-button:disabled {
    opacity: 0.6;
  }

  /* Dashboard sidebar styling - Apple Mac desktop app style - COLORS ONLY */
  .dashboard-sidebar {
    background: hsl(var(--background));
    border-right-color: hsl(var(--border));
  }

  .sidebar-nav-item {
    background: transparent;
    color: hsl(var(--muted-foreground));
  }

  .sidebar-nav-item:hover {
    background: hsl(var(--accent));
    color: hsl(var(--primary));
  }

  .sidebar-nav-item.active {
    background: hsl(var(--accent));
    color: hsl(var(--primary));
  }

  /* Dashboard card styling - COLORS ONLY */
  .dashboard-card {
    background: hsl(var(--card));
    border-color: hsl(var(--border));
  }

  /* Stats card styling - COLORS ONLY */
  .stats-card {
    background: hsl(var(--card));
    border-color: hsl(var(--border));
  }

  .stats-card:hover {
    background: hsl(var(--accent));
  }

  /* Progress indicator styling - COLORS ONLY */
  .progress-circle {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  /* Typography enhancements - COLORS ONLY */
  .dashboard-title {
    color: hsl(var(--foreground));
  }

  .dashboard-subtitle {
    color: hsl(var(--muted-foreground));
  }

  /* Quick action button styling - COLORS ONLY */
  .quick-action-button {
    background: hsl(var(--card));
    border-color: hsl(var(--border));
    color: hsl(var(--foreground));
  }

  .quick-action-button:hover {
    background: hsl(var(--accent));
    border-color: hsl(var(--primary));
  }

  /* Activity summary styling - COLORS ONLY */
  .activity-item {
    border-bottom-color: hsl(var(--border));
  }

  .activity-item:last-child {
    border-bottom: none;
  }
}
