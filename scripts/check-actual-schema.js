// Check the actual schema of user_goals table in mission_fresh
// HOLY RULE 0001: Real database schema inspection to match app to reality

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'

const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'mission_fresh'
  }
})

async function checkActualSchema() {
  console.log('🔍 Checking actual user_goals table schema in mission_fresh...')
  
  try {
    // Get existing data to understand the schema
    const { data: existingGoals, error: readError } = await supabase
      .from('user_goals')
      .select('*')
      .limit(1)
    
    if (readError) {
      console.error('❌ Failed to read existing goals:', readError)
      return
    }
    
    if (existingGoals && existingGoals.length > 0) {
      console.log('✅ Found existing goal record!')
      console.log('📋 Actual table structure:')
      
      const goal = existingGoals[0]
      Object.keys(goal).forEach(key => {
        const value = goal[key]
        const type = typeof value
        const isNull = value === null
        console.log(`  - ${key}: ${type}${isNull ? ' (null)' : ''} = ${JSON.stringify(value)}`)
      })
      
      console.log('\n🔧 Required fields for our app:')
      const requiredFields = [
        'id', 'user_id', 'goal_type', 'method', 'quit_date', 
        'motivation', 'typical_daily_usage', 'cost_per_unit', 
        'years_smoking', 'replacement_method', 'support_system', 
        'status', 'created_at', 'updated_at'
      ]
      
      const actualFields = Object.keys(goal)
      
      console.log('\n✅ Fields that match:')
      requiredFields.forEach(field => {
        if (actualFields.includes(field)) {
          console.log(`  ✅ ${field}`)
        }
      })
      
      console.log('\n❌ Missing fields:')
      requiredFields.forEach(field => {
        if (!actualFields.includes(field)) {
          console.log(`  ❌ ${field}`)
        }
      })
      
      console.log('\n🆕 Extra fields in database:')
      actualFields.forEach(field => {
        if (!requiredFields.includes(field)) {
          console.log(`  🆕 ${field}: ${typeof goal[field]} = ${JSON.stringify(goal[field])}`)
        }
      })
      
      console.log('\n💡 SOLUTION: Update TypeScript definitions to match actual database schema')
      console.log('📝 This will make the app 100% functional with existing data!')
      
    } else {
      console.log('⚠️  No existing goals found, table might be empty')
    }
    
  } catch (error) {
    console.error('💥 Schema check failed:', error)
  }
}

// Execute the schema check
checkActualSchema()
  .then(() => {
    console.log('\n🎉 Schema analysis completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Schema analysis failed:', error)
    process.exit(1)
  })
